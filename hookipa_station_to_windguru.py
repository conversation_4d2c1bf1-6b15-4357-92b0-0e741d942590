import os
import requests
from datetime import datetime
import time
import hashlib


# function to convert mph to knots
def m2k(mph):
    return round(float(mph) * 0.87,1)

# File path
file_path = 'WS8A.csv'

file_mtime = os.path.getmtime(file_path)
current_time = time.time()  # Get the current time in seconds since the epoch
# Check if the file has been updated within the last 5 minutes (300 seconds)
if current_time - file_mtime >= 300:
    print("File is stale (5 minutes), quitting early.")
    exit()

# Get the last modified time of the file in epoch format
last_modified_epoch = os.path.getmtime(file_path)
last_modified_iso = datetime.utcfromtimestamp(last_modified_epoch).isoformat() + "Z"

# Read the CSV file and split the data by commas
with open(file_path, 'r') as file:
    data = file.readline().strip().split(',')

# Extract values from the CSV data
avg_wind = m2k(data[0])  # Average wind speed (e.g., 13.1 mph)
gust_wind = m2k(data[1])  # Wind gust speed (e.g., 16.3 mph)
lull_wind = m2k(data[2])  # Lull wind speed (e.g., 10.7 mph) (not used here)
wind_cardinal = data[3]  # Wind cardinal direction (e.g., ENE)
wind_dir_deg = data[4]  # Wind direction degree (e.g., 69)
tempc = float(data[5])
tempf = (tempc * 9/5) + 32


uid = "windytron_hookipa"
salt = str(time.time())
password = "Jun1windguru"
combined_string = salt + uid + password
hash = hashlib.md5(combined_string.encode()).hexdigest()

url = "http://www.windguru.cz/upload/api.php"
params = {
    "uid": uid,
    "salt": salt,
    "hash": hash,
    "wind_avg": avg_wind,
    "wind_direction": wind_dir_deg,
    "wind_min": lull_wind,
    "wind_max": gust_wind,
    "temperature": tempc
}
print(params)
response = requests.get(url, params=params)

print(response.text)
