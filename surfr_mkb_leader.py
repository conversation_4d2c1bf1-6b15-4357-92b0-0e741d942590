#!/usr/bin/python3

import json
import requests
import os
import time
import sys
import argparse

#setup argument parser
parser = argparse.ArgumentParser()
parser.add_argument("-d", "--debug", help="debug mode", action='store_true')
parser.add_argument("-t", "--tidbyt", help="output to tidbyt with key", action='store_true')
args = parser.parse_args() 
debug = args.debug
tidbyt = True #args.tidbyt always do tidbyt, thanks.

# set the request url to the competition servlet
url = 'https://kiter-271715.appspot.com/CompetitionServlet?spotid=12411&ajax=0&video=0'

sample_string = '{"name":"TAVIS","country":"null","height":12.1,"airtime":5.8,"distance":44,"kmu":56,"landinggforce":4.9,"base64":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/"}'
last_data_string = ''
data = None
# each second, get the url and parse the json response
count = 0
status = "cleared"
# create a dict for leaderboard data
leaderboard = {}

#define a function add name and height to leaderboard dict
def add_to_leaderboard(data):
    if data['name'] not in leaderboard:
        leaderboard[data['name']] = data['height']
    elif data['height'] > leaderboard[data['name']]:
        leaderboard[data['name']] = data['height']

    # sort leaderboard by height
    leaderboard = sorted(leaderboard.items(), key=lambda x: x[1], reverse=True)
    # if name in the top 3 of leaderboard
    if data['name'] in leaderboard[:3]:
        return True
    else:
        return False

print("Started")
while True:

    if count > 9 and status == "displayed":
        # push the leaderboard to tidbyt
        command = '~/wildc.net/wind/pixlet/tidbyt_surfr_leader.sh'
        if debug: print(command)
        os.system(command)
        status = "cleared"

    if debug:
        data = json.loads(sample_string)
    else:
        if debug: print("Getting data")
        # try block to catch errors
        try:
            response = requests.get(url)
            # parse the json response
            if debug: print('Response: ' + response.text)
            data = json.loads(response.text)
        except:
            break

    # if we have data, and base64 key is present, delete the base64 key
    if data:
        if 'base64' in data:
            del data['base64']
        # if data is different from the last data, print the data
        if json.dumps(data) != last_data_string:
            # print the data
            print('.')
            print(data)
            last_data_string = json.dumps(data)
            
            if tidbyt: # on by default now
                # ./pixlet render surfr.star data='{"name":"TAVIS","country":"null","height":8.7,"airtime":5.8,"distance":44,"kmu":41,"landinggforce":4.9}'
                command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr.star data=\'' + json.dumps(data) + '\' && ./tidbyt_surfr.sh'
                if debug: print(command)
                os.system(command)
                count = 0
                status = "displayed"

            # build the system command
            command = '. ~/.bashrc ; cd ~/wildc.net/wind ; ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass \$\$titz\* -p maui/surfr_mkb -r -m {}'.format(json.dumps(data))
            # print the commmand
            if debug: print(command)
            # execute system command
            os.system(command)
            print('+', end='')
            # add data to leaderboard dict; returns true if in top 3
            if add_to_leaderboard(data):
                # execute the pixlet render command to generate the leaderboard
                command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_high.star data=\'' + json.dumps(leaderboard[:3]) + '\' && ./tidbyt_surfr_high.sh'
                if debug: print(command)
                os.system(command)

        else:
            print('-',end='')

    # if debug is true, quit the program
    if debug:
        break

    # wait one second
    time.sleep(1)
    print('.', end='')
    sys.stdout.flush()
    count = count + 1

    

