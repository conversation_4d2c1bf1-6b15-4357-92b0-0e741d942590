#!/usr/bin/env python3
"""
NOAA NDBC Buoy Data Fetcher
Similar to ik_multi.py but for NOAA buoy data
"""

import requests
import re
import json
from datetime import datetime, timezone


def fetch_and_process_buoys(spots_dict):
    """
    Fetch and process buoy data for all spots in spots_dict
    Combined function that fetches and immediately processes each buoy

    Args:
        spots_dict: Dictionary in same format as ik_multi
                   Keys are spot names, values contain 'id' field with buoy ID

    Returns:
        Dictionary with processed data for each spot
    """
    processed_data = {}

    for spot_name, spot_info in spots_dict.items():
        buoy_id = spot_info.get('id')
        if not buoy_id:
            print(f"No buoy ID found for {spot_name}")
            continue

        print(f"Fetching data for {spot_name} (buoy {buoy_id})")

        try:
            # Fetch the NOAA buoy page
            url = f"https://www.ndbc.noaa.gov/station_page.php?station={buoy_id}&uom=E&tz=GMT"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=20)
            response.raise_for_status()

            rawhtml = response.text

            # Try multiple regex patterns for swell height
            height_ft = 0.0
            height_patterns = [
                r'Swell\s+Height\s+\(SwH\):</td><td[^>]*>(\d+\.\d+)\s*ft</td>',
                r'Swell\s+Height\s+\(SwH\):\s*</td>\s*<td[^>]*>\s*(\d+\.\d+)\s*ft\s*</td>',
                r'SwH[^>]*>(\d+\.\d+)\s*ft',
                r'Swell.*?Height.*?(\d+\.\d+)\s*ft'
            ]

            for pattern in height_patterns:
                height_match = re.search(pattern, rawhtml, re.IGNORECASE | re.DOTALL)
                if height_match:
                    height_ft = float(height_match.group(1))
                    break

            # Try multiple regex patterns for swell period
            period_sec = 0
            period_patterns = [
                r'Swell\s+Period\s+\(SwP\):</td><td[^>]*>(\d+)\.?\d?\s*sec\s*</td>',
                r'Swell\s+Period\s+\(SwP\):\s*</td>\s*<td[^>]*>\s*(\d+)\.?\d?\s*sec\s*</td>',
                r'SwP[^>]*>(\d+)\.?\d?\s*sec',
                r'Swell.*?Period.*?(\d+)\.?\d?\s*sec'
            ]

            for pattern in period_patterns:
                period_match = re.search(pattern, rawhtml, re.IGNORECASE | re.DOTALL)
                if period_match:
                    period_sec = int(period_match.group(1))
                    break

            # Try multiple regex patterns for swell direction
            direction = "N"
            dir_patterns = [
                r'Swell\s+Direction\s+\(SwD\):</td><td[^>]*>([ENSW]+)',
                r'Swell\s+Direction\s+\(SwD\):\s*</td>\s*<td[^>]*>\s*([ENSW]+)',
                r'SwD[^>]*>([ENSW]+)',
                r'Swell.*?Direction.*?([ENSW]+)'
            ]

            for pattern in dir_patterns:
                dir_match = re.search(pattern, rawhtml, re.IGNORECASE | re.DOTALL)
                if dir_match:
                    direction = dir_match.group(1)
                    break

            # Parse UTC timestamp from the page
            utc_stamp = ""
            local_stamp = ""

            utc_pattern = r'<tr class="right"><th><span class="nowrap">(\d{4}-\d{2}-\d{2})</span>\s*<span class="nowrap">(\d{4})</span></th>'
            utc_match = re.search(utc_pattern, rawhtml)

            if utc_match:
                date_str = utc_match.group(1)  # e.g., "2025-06-29"
                time_str = utc_match.group(2)  # e.g., "0056"

                # Convert time from HHMM to HH:MM format
                hour = time_str[:2]
                minute = time_str[2:]

                # Create UTC timestamp
                utc_stamp = f"{date_str}T{hour}:{minute}:00"

                # Convert to local time for stamp field
                try:
                    utc_dt = datetime.fromisoformat(utc_stamp).replace(tzinfo=timezone.utc)
                    local_dt = utc_dt.astimezone()
                    local_stamp = local_dt.strftime("%Y-%m-%dT%H:%M:%S")
                except Exception as e:
                    # Fallback to current time if parsing fails
                    print(f"  Warning: Error parsing timestamp: {e}")
                    utc_now = datetime.now(timezone.utc)
                    utc_stamp = utc_now.strftime("%Y-%m-%dT%H:%M:%S")
                    local_stamp = utc_now.astimezone().strftime("%Y-%m-%dT%H:%M:%S")
            else:
                # Fallback to current time if no timestamp found
                print("  Warning: Could not find UTC timestamp in page, using current time")
                utc_now = datetime.now(timezone.utc)
                utc_stamp = utc_now.strftime("%Y-%m-%dT%H:%M:%S")
                local_stamp = utc_now.astimezone().strftime("%Y-%m-%dT%H:%M:%S")

            print(f"  Height: {height_ft}ft, Period: {period_sec}s, Direction: {direction}")

            # Get current time for utc_last_fetch (when script is running)
            fetch_time = datetime.now(timezone.utc)
            utc_last_fetch = fetch_time.strftime("%Y-%m-%dT%H:%M:%S")

            # Create swell string in format "3.3f,12s,ESE"
            swell_string = f"{height_ft}f,{period_sec}s,{direction}"

            # Handle basename (spot name)
            if "filename" in spot_info:
                basename = spot_info['filename']
            elif "name" in spot_info:
                basename = spot_info['name']
            else:
                basename = spot_name

            # Handle label
            if "name" in spot_info:
                label = spot_info['name'].replace('_', ' ').title()
            else:
                label = spot_name.replace('_', ' ').title()

            # Build data dictionary in NOAA buoy format
            spot_data = {
                "stamp": local_stamp,
                "height": height_ft,
                "period": period_sec,
                "dir_card": direction,
                "label": label,
                "swell_string": swell_string,
                "utc_stamp": utc_stamp,
                "utc_last_fetch": utc_last_fetch
            }

            # Store in results
            processed_data[basename] = spot_data

        except Exception as e:
            print(f"Error fetching/processing data for {spot_name}: {e}")

    return processed_data


def main(spots_dict, debug=False):
    """
    Main function to fetch and process NOAA buoy data

    Args:
        spots_dict: Dictionary of spots in same format as ik_multi
        debug: If True, use debug data instead of fetching

    Returns:
        Dictionary with processed data for each spot
    """

    if not debug:
        return fetch_and_process_buoys(spots_dict)
    else:
        print("Using debug data for NOAA NDBC")
        processed_data = {}

        for spot_name, spot_info in spots_dict.items():
            utc_stamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S")
            local_stamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
            utc_last_fetch = utc_stamp  # Same as utc_stamp for debug mode

            # Handle basename and label
            if "filename" in spot_info:
                basename = spot_info['filename']
            elif "name" in spot_info:
                basename = spot_info['name']
            else:
                basename = spot_name

            if "name" in spot_info:
                label = spot_info['name'].replace('_', ' ').title()
            else:
                label = spot_name.replace('_', ' ').title()

            # Debug data
            spot_data = {
                "stamp": local_stamp,
                "height": 2.5,
                "period": 8,
                "dir_card": "NE",
                "label": label,
                "swell_string": "2.5f,8s,NE",
                "utc_stamp": utc_stamp,
                "utc_last_fetch": utc_last_fetch
            }

            processed_data[basename] = spot_data

        return processed_data


if __name__ == "__main__":
    import sys

    if len(sys.argv) >= 3:
        # Command line usage: python3 noaa_ndbc.py <buoy_id> <spot_name> [debug]
        buoy_id = sys.argv[1]
        spot_name = sys.argv[2]
        debug_mode = len(sys.argv) > 3 and sys.argv[3].lower() == 'debug'

        test_spots = {
            spot_name: {
                'id': int(buoy_id),
                'data_types': ['swell'],
                'provider': 'noaa_ndbc',
                'type': 'buoy'
            }
        }

        print(f"Testing NOAA NDBC for buoy {buoy_id} (spot: {spot_name})")
        if debug_mode:
            print("Using debug mode")

    else:
        # Default test with sample data
        test_spots = {
            'mokapu_point': {
                'id': 51202,
                'data_types': ['swell'],
                'provider': 'noaa_ndbc',
                'type': 'buoy'
            }
        }
        debug_mode = False
        print("Using default test data (mokapu_point, buoy 51202)")
        print("Usage: python3 noaa_ndbc.py <buoy_id> <spot_name> [debug]")
        print("Example: python3 noaa_ndbc.py 51202 mokapu_point")

    result = main(spots_dict=test_spots, debug=debug_mode)
    print("\nResults:")
    for spot_name, data in result.items():
        print(f"\n{data['label']}:")
        print(f"  Swell: {data['swell_string']}")
        print(f"  Height: {data['height']}ft")
        print(f"  Period: {data['period']}s")
        print(f"  Direction: {data['dir_card']}")
        print(f"  UTC Timestamp: {data['utc_stamp']}")
        print(f"  Local Timestamp: {data['stamp']}")

        # Write JSON file for testing
        json_filename = f"{spot_name}.json"
        with open(json_filename, "w") as f:
            json.dump(data, f, indent=2)
        print(f"  Wrote: {json_filename}")

        print
