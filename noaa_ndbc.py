#!/usr/bin/env python3
"""
NOAA NDBC Buoy Data Fetcher
Similar to ik_multi.py but for NOAA buoy data
"""

import requests
import re
import json
from datetime import datetime, timezone


def fetch_buoy_data(spots_dict):
    """
    Fetch buoy data for all spots in spots_dict
    
    Args:
        spots_dict: Dictionary in same format as ik_multi
                   Keys are spot names, values contain 'id' field with buoy ID
    
    Returns:
        Updated spots_dict with 'data' and 'fields' added to each spot
    """
    
    for spot_name, spot_info in spots_dict.items():
        buoy_id = spot_info.get('id')
        if not buoy_id:
            print(f"No buoy ID found for {spot_name}")
            continue
            
        print(f"Fetching data for {spot_name} (buoy {buoy_id})")
        
        try:
            # Fetch the NOAA buoy page
            url = f"https://www.ndbc.noaa.gov/station_page.php?station={buoy_id}"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=20)
            response.raise_for_status()
            
            rawhtml = response.text

            # Optional: save HTML to file for debugging
            # with open(f"debug_buoy_{buoy_id}.html", "w") as f:
            #     f.write(rawhtml)

            # Try multiple regex patterns for swell height
            height_ft = 0.0
            height_patterns = [
                r'Swell\s+Height\s+\(SwH\):</td><td[^>]*>(\d+\.\d+)\s*ft</td>',
                r'Swell\s+Height\s+\(SwH\):\s*</td>\s*<td[^>]*>\s*(\d+\.\d+)\s*ft\s*</td>',
                r'SwH[^>]*>(\d+\.\d+)\s*ft',
                r'Swell.*?Height.*?(\d+\.\d+)\s*ft'
            ]

            for pattern in height_patterns:
                height_match = re.search(pattern, rawhtml, re.IGNORECASE | re.DOTALL)
                if height_match:
                    height_ft = float(height_match.group(1))
                    break

            # Try multiple regex patterns for swell period
            period_sec = 0
            period_patterns = [
                r'Swell\s+Period\s+\(SwP\):</td><td[^>]*>(\d+)\.?\d?\s*sec\s*</td>',
                r'Swell\s+Period\s+\(SwP\):\s*</td>\s*<td[^>]*>\s*(\d+)\.?\d?\s*sec\s*</td>',
                r'SwP[^>]*>(\d+)\.?\d?\s*sec',
                r'Swell.*?Period.*?(\d+)\.?\d?\s*sec'
            ]

            for pattern in period_patterns:
                period_match = re.search(pattern, rawhtml, re.IGNORECASE | re.DOTALL)
                if period_match:
                    period_sec = int(period_match.group(1))
                    break

            # Try multiple regex patterns for swell direction
            direction = "N"
            dir_patterns = [
                r'Swell\s+Direction\s+\(SwD\):</td><td[^>]*>([ENSW]+)',
                r'Swell\s+Direction\s+\(SwD\):\s*</td>\s*<td[^>]*>\s*([ENSW]+)',
                r'SwD[^>]*>([ENSW]+)',
                r'Swell.*?Direction.*?([ENSW]+)'
            ]

            for pattern in dir_patterns:
                dir_match = re.search(pattern, rawhtml, re.IGNORECASE | re.DOTALL)
                if dir_match:
                    direction = dir_match.group(1)
                    break

            # Parse UTC timestamp from the page
            # Look for pattern: <tr class="right"><th><span class="nowrap">2025-06-29</span> <span class="nowrap">0056</span></th>
            utc_stamp = ""
            local_stamp = ""

            utc_pattern = r'<tr class="right"><th><span class="nowrap">(\d{4}-\d{2}-\d{2})</span>\s*<span class="nowrap">(\d{4})</span></th>'
            utc_match = re.search(utc_pattern, rawhtml)

            if utc_match:
                date_str = utc_match.group(1)  # e.g., "2025-06-29"
                time_str = utc_match.group(2)  # e.g., "0056"

                # Convert time from HHMM to HH:MM format
                hour = time_str[:2]
                minute = time_str[2:]

                # Create UTC timestamp
                utc_stamp = f"{date_str}T{hour}:{minute}:00"

                # Convert to local time for stamp field
                try:
                    utc_dt = datetime.fromisoformat(utc_stamp).replace(tzinfo=timezone.utc)
                    local_dt = utc_dt.astimezone()
                    local_stamp = local_dt.strftime("%Y-%m-%dT%H:%M:%S")
                except Exception as e:
                    # Fallback to current time if parsing fails
                    print(f"  Warning: Error parsing timestamp: {e}")
                    utc_now = datetime.now(timezone.utc)
                    utc_stamp = utc_now.strftime("%Y-%m-%dT%H:%M:%S")
                    local_stamp = utc_now.astimezone().strftime("%Y-%m-%dT%H:%M:%S")
            else:
                # Fallback to current time if no timestamp found
                print("  Warning: Could not find UTC timestamp in page, using current time")
                utc_now = datetime.now(timezone.utc)
                utc_stamp = utc_now.strftime("%Y-%m-%dT%H:%M:%S")
                local_stamp = utc_now.astimezone().strftime("%Y-%m-%dT%H:%M:%S")
            
            print(f"  Height: {height_ft}ft, Period: {period_sec}s, Direction: {direction}")
            
            # Store data in same format as ik_multi
            # Using a simple data array format similar to weatherflow
            spot_info['data'] = [
                local_stamp,        # 0: local timestamp
                f"{height_ft}f,{period_sec}s,{direction}",  # 1: swell description
                height_ft,          # 2: height (numeric)
                period_sec,         # 3: period (numeric) 
                direction,          # 4: direction (cardinal)
                0,                  # 5: direction degrees (not available)
                utc_stamp,          # 6: UTC timestamp
            ]
            
            spot_info['fields'] = [
                "timestamp",
                "swell_desc", 
                "height",
                "period",
                "dir_text",
                "dir_deg",
                "utc_timestamp"
            ]
            
            spot_info['utc_last_fetch'] = utc_stamp
            
        except Exception as e:
            print(f"Error fetching data for {spot_name}: {e}")
            # Set empty data on error
            spot_info['data'] = [None] * 7
            spot_info['fields'] = ["timestamp", "swell_desc", "height", "period", "dir_text", "dir_deg", "utc_timestamp"]
    
    return spots_dict


def process_spots(spots_dict):
    """
    Process spots data into final JSON format
    Similar to ik_multi.process_spots()
    
    Args:
        spots_dict: Dictionary with buoy data
        
    Returns:
        Dictionary with processed data for each spot
    """
    processed_data = {}
    
    for key, spot in spots_dict.items():
        try:
            if 'data' not in spot or not spot['data'] or spot['data'][0] is None:
                print(f"No data available for {key}")
                continue
                
            # Extract data from the data array
            local_stamp = spot['data'][0] or ""
            height_ft = float(spot['data'][2] or 0)
            period_sec = int(spot['data'][3] or 0)
            direction = spot['data'][4] or "N"
            utc_stamp = spot['data'][6] or ""
            
            # Create swell string in format "3.3f,12s,ESE"
            swell_string = f"{height_ft}f,{period_sec}s,{direction}"
            
            # Handle basename (spot name)
            if "filename" in spot:
                basename = spot['filename']
            elif "name" in spot:
                basename = spot['name']
            else:
                basename = key
                
            # Handle label
            if "name" in spot:
                label = spot['name'].replace('_', ' ').title()
            else:
                label = key.replace('_', ' ').title()
            
            # Build data dictionary in NOAA buoy format
            spot_data = {
                "stamp": local_stamp,
                "height": height_ft,
                "period": period_sec,
                "dir_card": direction,
                "label": label,
                "swell_string": swell_string,
                "utc_stamp": utc_stamp
            }
            
            # Add raw data
            raw_data = {
                k: v
                for k, v in zip(spot["fields"], spot["data"])
                if v is not None
            }
            spot_data['raw_data'] = raw_data
            
            # Store in results
            processed_data[basename] = spot_data
            
        except Exception as e:
            print(f"Error processing spot {key}: {e}")
    
    return processed_data


def main(spots_dict, debug=False):
    """
    Main function to fetch and process NOAA buoy data
    
    Args:
        spots_dict: Dictionary of spots in same format as ik_multi
        debug: If True, use debug data instead of fetching
        
    Returns:
        Dictionary with processed data for each spot
    """
    
    if not debug:
        spots_dict = fetch_buoy_data(spots_dict)
    else:
        print("Using debug data for NOAA NDBC")
        # Add some debug data
        for spot_name, spot_info in spots_dict.items():
            utc_stamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S")
            local_stamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
            
            spot_info['data'] = [
                local_stamp,
                "2.5f,8s,NE",
                2.5,
                8,
                "NE", 
                0,
                utc_stamp
            ]
            spot_info['fields'] = ["timestamp", "swell_desc", "height", "period", "dir_text", "dir_deg", "utc_timestamp"]
            spot_info['utc_last_fetch'] = utc_stamp
    
    if spots_dict:
        return process_spots(spots_dict)
    else:
        return {}


if __name__ == "__main__":
    # Test with sample data
    test_spots = {
        'mokapu_point': {
            'id': 51202,
            'data_types': ['swell'],
            'provider': 'noaa_ndbc',
            'type': 'buoy'
        }
    }
    
    result = main(spots_dict=test_spots, debug=False)
    print("\nResults:")
    for spot_name, data in result.items():
        print(f"\n{data['label']}:")
        print(f"  Swell: {data['swell_string']}")
        print(f"  Height: {data['height']}ft")
        print(f"  Period: {data['period']}s") 
        print(f"  Direction: {data['dir_card']}")
        print(f"  Timestamp: {data['stamp']}")
