#!/usr/bin/perl
use strict;
my $buoy_id = shift @ARGV;
my $buoy_name = shift @ARGV;



my $url = "http://www.ndbc.noaa.gov/station_page.php?station=".$buoy_id;
#print $url;
my $rawrss = `wget --timeout=10 --cache=off -o /dev/null -O - $url`;
chomp $rawrss;
$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Height is $height\n";

$rawrss =~ /\<td\>Dominant\sWave\sPeriod\s\(DPD\):\<\/td\>\s+\<td\>\s+(\d+\s+sec)\<\/td\>/;
#$rawrss =~ /Swell\sPeriod\s\(SwP\):\<\/td\>.*\<td\>\s?(\d+\.?\d?\ssec)/;

#.+(\d+\.\d?\s+sec)\<\/td/;
my $period = $1;
$period =~ s/\s//;
$period =~ s/sec/s/;


print "Period is $period\n";

#$rawrss =~ /\<td\>Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\s+\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
#<td>Swell Direction (SwD):</td>
#<td>NNW</td>
#$rawrss =~ /\<td\>Swell\sDirection\s+\(SwD\):\<\/td\>\s?\<td\>\s*([ENSW]+)\<\/td\>/;
$rawrss =~ /\<td\>Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\s+\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
my $direction = $1;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;
print "Direction is $direction deg \n";

unless ($height eq '' || $period eq '' || $direction eq '') {
    my $log_file_name;
    if ($buoy_name eq '') {
        $log_file_name = "noaa_buoy_".$buoy_id.".txt";
    } else {
        $log_file_name = "noaa_buoy_".$buoy_id."_".$buoy_name.".txt";
    }
    my $LOG;
    open $LOG , ">$log_file_name" ;	
    print $LOG $height.",". $period . "," . $direction . "\n";
    close $LOG;
}
