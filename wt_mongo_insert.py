from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi
import sys,json

def load_json_file(file_path):
    try:
        with open(file_path, 'r') as file:
            data = json.load(file)
            return data
    except FileNotFoundError:
        print(f"The file '{file_path}' was not found.")
    except json.JSONDecodeError:
        print(f"The file '{file_path}' does not contain valid JSON.")
    except Exception as e:
        print(f"An error occurred: {e}")
    return None

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python script_name.py <file_path>")
        exit(1)
    else:
        file_path = sys.argv[1]
        wind = load_json_file(file_path)
print(f"wind data is : {wind}")
wind_csv = f"{wind['avg']},{wind['gust']},{wind['lull']},{wind['dir_card']},{wind['dir_deg']}"
uri = "mongodb+srv://windytron:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
# Create a new client and connect to the server
client = MongoClient(uri, server_api=ServerApi('1'))
db = client["windytron_stations"]
readings = db[f"{wind['label'].lower()}_csv"]

readings.insert_one({"created_at" : wind['stamp'],
            "csv" : wind_csv
})

                    
