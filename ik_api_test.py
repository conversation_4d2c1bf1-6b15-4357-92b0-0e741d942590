# If run once per minute,  check the corresponding json file for the timestamp and if we will update within the minute, wait and do it, otherwise wait for next time round

import requests
import re
import os
import sys
import json
import time
from datetime import datetime
from datetime import timedelta
spot_name = sys.argv[1]

date_object = None
old_stamp_str = "blah"
print("\nStarting____________________________________")
try:
    force = sys.argv[2]  #  another argument means to run without doing any timing.
except:
    force = False
spotdict = {
  "kanaha": "166192",
  "kahului": "4349",
  "kihei": "181145",
  "la_ventana": "183476",
  "ogg": "643"
}
try:
    spot_id = spotdict[spot_name]
except:
    print("bad spot name")
    exit(1)

# function to convert mph to knots
def m2k(mph):
    return int((mph * 0.87)+0.5)

def secs_until_update(spot):
    global old_stamp_str
    global date_object
    # pull the json file and pull the current min:sec
    in_file = open(spot_name+".json", "r")
    spot_json = in_file.read().rstrip()
    #print(spot_json)
    j = json.loads(spot_json)
    stamp_str = j["spots"][0]["stations"][0]["data_values"][0][12].split('.')[0]
    old_stamp_str = stamp_str
    #d datetime.strptime('2021-05-28 13:49:16-1000', "%Y-%m-%d %H:%M:%S") #2021-05-28 13:49:16-1000
    date_object = datetime.strptime(stamp_str, "%Y-%m-%d %H:%M:%S") #2021-05-28 13:49:16-1000
    print("json:", date_object)
    
    #5 minute timedelta
    delta_5min = timedelta(minutes = 5)
                 
    now = datetime.utcnow()   ## DST FUCKUP HERE
    print ("now_: " + str(now))
    update_delta = (date_object+delta_5min) - now
    sec_delta = update_delta.total_seconds()
    print("sec_delta:" + str(sec_delta), end='') 
    if ( sec_delta < 45 and sec_delta > 0 ):
        # delay for seconds and then do the update
        print ("seconds: "+ str(sec_delta))
        return sec_delta
    elif (sec_delta < 0):  # negative means we missed the update, go now
        print("returning 0--", end = '')
        return 0
    else : 
        #print("returning 120") # this just forces a wait
        return 120

    

secs = int(secs_until_update(spot_name)) 
if ( secs < 60 ):
    print ("sleeping for "+str(secs+10))
    if (secs >= 0): time.sleep(secs+10)
elif( not force):
    #print ("aborting");
    exit(1)
else:
    print("forced continue")


api_url = 'http://api.weatherflow.com/wxengine/rest/spot/getSpotDetailSetByList?units_wind=mph&spot_list='+spot_id+'&wf_token='

# returns wftoken
def wf_login():
    print( "doing full login" )
    post_data = {
        'isun' : '<EMAIL>',
        'ispw' : 'jungle',
        'iwok.x' : 'Sign In',
        'app' : '' ,
        'rd' : '' 
    }
    post_headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192',
        'Content-Type':  'application/x-www-form-urlencoded'
    }
    url1 = 'https://secure.ikitesurf.com/'
 
    print("posting"+url1)
    resp2 = requests.head(url1, data=post_data)
    headers = str(resp2.headers)
    print(headers)
    token = re.search("wfToken=(.*);expires", headers ).group(1)
    
    with open("wftoken", "w") as wf_token_file:
        wf_token_file.write(token)
    
    return token

def fetch_data(token):
    global date_object
    global old_stamp_str
    response = requests.get(api_url+token)
    result = response.text
#    print (response.headers)
#    print (result)
    j = json.loads(result)
    data = j["spots"][0]["stations"][0]["data_values"][0][2:7]
    if re.search("Invalid weatherflow token|paid membership",result) or data[0] is None:
        # token has gone stale, refresh
        token = wf_login()
        response = requests.get(api_url+token)
        result = response.text
        
    j = json.loads(result)
#    print (j)
    try:
        data = j["spots"][0]["stations"][0]["data_values"][0][2:7]
    except:
        print("bad json data structure")
        exit(1)
    
    # test for stale data by opening last downloaded json file and checking stamp
    # load previous json file
    new_stamp = j["spots"][0]["stations"][0]["data_values"][0][12].split('.')[0]
    print(new_stamp)
    print(old_stamp_str)
    if new_stamp == old_stamp_str and not force:
        print("no update, quitting")
        exit(1)

    # new_stamp_str = j["spots"][0]["stations"][0]["data_values"][0][0].split('.')[0]
    # new_date = datetime.strptime(new_stamp_str, "%Y-%m-%d %H:%M:%S") #2021-05-28 13:49:16-1000
    # print("newjson: " + str(new_stamp_str))
    # if (new_stamp_str == old_stamp_str):
    #     # we have no new data, just exit
    #     print("stale data, exiting")
    #     exit(1)

    trimmed_json = dict()
    trimmed_json["spots"] = j["spots"]  
    print("trimed json:" + json.dumps(trimmed_json))
    # write out json data to file for retrieval by gto2 devices
    print("writing to:"+ spot_name+".json", end='')
    with open(spot_name+'.json', "w") as json_file:
        json_file.write(json.dumps(trimmed_json))      
    return data
    

# restore session object from filesystem
exists = os.path.isfile("wftoken")
if exists:
    in_file = open("wftoken", "r")
    wftoken = in_file.read().rstrip()
else:
    wftoken = wf_login()
    
data = fetch_data(wftoken)

# use OGG for direction until kb station is fixed 2021/10/03
# supposedly fixed 1021/10/14
if (spot_name == "blah"):      # no spot named blah so it's disabled
    print("using OGG for direction")
    api_url = 'http://api.weatherflow.com/wxengine/rest/spot/getSpotDetailSetByList?units_wind=mph&spot_list=643&wf_token='
    data_ogg = fetch_data(wftoken)
    wind_dir = data_ogg[4].strip('"')
    wind_dir_degrees = data_ogg[3]
else:
    wind_dir = data[4].strip('"')
    wind_dir_degrees = data[3]            

# assign the wind data
#print(data)
#[2.1, 0.9, 3.6, 270,"W"]
# wind_string = data_array[1].strip('"')
wind_avg = int(data[0]+0.5)
wind_lull = int(data[1]+0.5) 
wind_gust = int(data[2]+0.5)

# #ENE 56 16g20
wind_string = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_avg) + "g" + str(wind_gust)
wind_kts = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(m2k(data[0])) + "g" + str(m2k(data[2]))
wind_csv = str(wind_avg) + "," + str(wind_gust) +"," + str(wind_lull)  + "," + str(wind_dir) + "," + str(wind_dir_degrees)
wind_lag =  str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_lull) + "-" + str(wind_avg) + "g" + str(wind_gust)
print (wind_string)
# 
# # need to output this : ENE 56 16g20
with open(spot_name, "w") as text_file:
    text_file.write(wind_string)

with open(spot_name+"_kts", "w") as text_file:
    text_file.write(wind_kts)

spot_csv = spot_name+".csv"
with open(spot_csv, "w") as text_file:
    text_file.write(wind_csv)
    
# output wind_lag to file
with open(spot_name+"_lag", "w") as text_file:
    text_file.write(wind_lag)

#print("done")
exit(0)
