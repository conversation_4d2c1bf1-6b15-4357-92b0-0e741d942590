# https://ww.windalert.com/cgi-bin/tideGraph.gif?&beginTime=2024-4-30%2000:00:00&endTime=2024-5-1%2023:59:59&width=830&height=290&tideSiteName=Kahului,+Kahului+Harbor,+Hawaii
# https://ww.windalert.com/cgi-bin/tideGraph.gif?
#   &beginTime={bg}
#   &endTime={2024-5-1%2023:59:59}
#   &width=830&height=290&tideSiteName=Kahului,+Kahului+Harbor,+Hawaii

from datetime import datetime
import os
import requests

# Example URL with placeholders for beginDate and endDate substitution
base_url = "https://ww.windalert.com/cgi-bin/tideGraph.gif?&beginTime={}&endTime={}&width=830&height=290&tideSiteName=Kahului,+Kahului+Harbor,+Hawaii"

# Get the current date and time
current_date = datetime.now()

# Format the date string as "YYYY-MM-DD HH:mm:ss"
date_begin = current_date.strftime("%Y-%m-%d 00:00:00").replace(' ','%20')
date_end = current_date.strftime("%Y-%m-%d 23:59:59").replace(' ','%20')

final_url = base_url.format(date_begin,date_end)
print(final_url)  # Output: Current date and time in "YYYY-MM-DD HH:mm:ss" format

# Send a GET request to the URL
response = requests.get(final_url)

# Check if the request was successful (status code 200)
if response.status_code == 200:
    if len(response.content) > 0:
        print("URL fetched successfully:")
        with open("kahului_tide_graph.gif", "wb") as file:
            file.write(response.content)
    else:
        print("Got zero size file ;(")
else:
    print("Error fetching URL. Status code:", response.status_code)

