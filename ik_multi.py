# Multi spot ik fetch
# Fetch all the spots in one request.

import requests
import re
import os
import sys
import json
import subprocess

# returns wftoken
def wf_login():
    print( "doing full login" )
    post_data = {
        'isun' : '<EMAIL>',
        'ispw' : 'jungle',
        'iwok.x' : 'Sign In',
        'app' : '' ,
        'rd' : '' 
    }
    post_headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192',
        'Content-Type':  'application/x-www-form-urlencoded'
    }
    url1 = 'https://secure.ikitesurf.com/'
 
    #print("posting"+url1)
    resp2 = requests.head(url1, data=post_data)
    headers = str(resp2.headers)
    #print(headers)
    token = re.search("wfToken=(.*);expires", headers ).group(1)
    
    with open("wftoken", "w") as wf_token_file:
        wf_token_file.write(token)
    
    return token

# populate the spotdict variable
def fetch_data(token, spotdict):
    post_headers = {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "dnt": "1",
        "referer": "https://wx.ikitesurf.com/",
        "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "script",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "cross-site",
        "sec-fetch-storage-access": "none",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }

    # Handle both old format (spot_id as key) and new format (spot_name as key with 'id' field)
    spot_ids = []
    for key, value in spotdict.items():
        if isinstance(value, dict) and 'id' in value:
            # New format: spot_name as key, 'id' field contains spot_id
            spot_ids.append(str(value['id']))
        else:
            # Old format: spot_id as key
            spot_ids.append(str(key))

    spot_list = ','.join(spot_ids)
    api_url = f'http://api.weatherflow.com/wxengine/rest/spot/getSpotDetailSetByList?units_wind=mph&spot_list={spot_list}&wf_token='

    # First attempt with existing token
    response = requests.get(api_url+token, headers=post_headers)    
    result = response.text

    if re.search("Invalid weatherflow token|paid membership|unauthorized", result):
        # Token has gone stale or is not yet active, refresh
        print("Token invalid or not yet active, getting new token...")
        token = wf_login()

        # Add a small delay to allow token to activate
        import time
        time.sleep(2)

        # Try again with new token
        print(f"Retrying with new token: {token[:10]}...")
        response = requests.get(api_url+token, headers=post_headers)
        result = response.text

        # If still failing, we need to exit and try again later
        if re.search("Invalid weatherflow token|paid membership|unauthorized", result):
            print("New token not yet active. Please run the script again in a few seconds.")
            # Save the token so next run will use it
            with open("wftoken", "w") as wf_token_file:
                wf_token_file.write(token)
            return {}

    try:
        j = json.loads(result)
    except json.JSONDecodeError:
        print(f"Failed to parse JSON response: {result[:100]}...")
        return {}

    if ("spots" not in j):
        print(f"Error in fetch: {j}")
        return {}

    for i in j["spots"]:
        spot_id = str(i["spot_id"])
        print(f"Processing spot {spot_id}")
        try:
            # Handle both old format (spot_id as key) and new format (spot_name as key with 'id' field)
            target_key = None
            for key, value in spotdict.items():
                if isinstance(value, dict) and 'id' in value:
                    # New format: check if this spot's id matches
                    if str(value['id']) == spot_id:
                        target_key = key
                        break
                else:
                    # Old format: check if key matches spot_id
                    if str(key) == spot_id:
                        target_key = key
                        break

            if target_key:
                spotdict[target_key]["data"] = i["stations"][0]["data_values"][0]
                spotdict[target_key]["fields"] = i["data_names"]
                spotdict[target_key]["utc_last_fetch"] = j["current_time_utc"]
        except Exception as e:
            print(f"Error processing spot {spot_id}: {e}")
    # Write out fetch data to json file
    with open("ik_last_fetch.json", "w") as json_file:
        json.dump(spotdict, json_file, indent=2)

    return spotdict

def get_token():
    """Get token from file or login"""
    exists = os.path.isfile("wftoken")
    if exists:
        with open("wftoken", "r") as in_file:
            return in_file.read().rstrip()
    else:
        return wf_login()

def process_spots(spotdict):
    """Process spots and optionally write output files
    
    Args:
        spotdict: Dictionary of spots with data
        extra_scripts: List of scripts to run after processing
        output_files: Whether to write output files (default: True)
        
    Returns:
        Dictionary with processed data for each spot
    """
    processed_data = {}

    for key, spot in spotdict.items():
        try:
            wind_avg = int(spot['data'][0+2] or 0 +0.5)
            wind_gust = int(spot['data'][2+2] or 0 +0.5)
            wind_lull = 0
            if spot['data'][1+2]:
                wind_lull = int(spot['data'][1+2] or 0 +0.5)
                if wind_lull > wind_avg:
                    wind_lull = 0
            wind_dir = spot['data'][4+2].strip('"')
            wind_dir_degrees = spot['data'][3+2]
            local_stamp = spot['data'][0].replace(' ','T')
            utc_stamp = spot['data'][12].replace(' ','T')
            utc_last_fetch = spot["utc_last_fetch"].replace(" ", "T")

            wind_string = f"{wind_dir} {wind_dir_degrees} {wind_avg}g{wind_gust}"

            # Handle both old and new format for determining basename
            if "filename" in spot:
                basename = spot['filename']
            elif "name" in spot:
                # Old format: spot has 'name' field
                basename = spot['name']
            else:
                # New format: key is the spot name
                basename = key

            # Build data dictionary
            # Handle label generation for both old and new format
            if "name" in spot:
                # Old format: spot has 'name' field
                label = spot['name'].replace('_',' ').title()
            else:
                # New format: key is the spot name
                label = key.replace('_',' ').title()

            spot_data = {
                "avg": wind_avg,
                "gust": wind_gust,
                "lull": wind_lull,
                "dir_card": wind_dir,
                "dir_deg": wind_dir_degrees,
                "stamp": local_stamp,
                "utc_stamp": utc_stamp,
                "utc_last_fetch": utc_last_fetch,
                "label": label,
                "wind_string": wind_string
            }

            raw_data = {
                k: v
                for k, v in zip(spot["fields"], spot["data"])
                if v is not None
            }

            spot_data['raw_data'] = raw_data

            # Add extra fields if specified
            if "extra_fields" in spot:
                for field_index in spot['extra_fields']:
                    spot_data[spot["fields"][field_index]] = spot["data"][field_index]

            # Store in results dictionary
            processed_data[basename] = spot_data

            # # Output files if requested
            # if output_files:
            #     if 'bare_output' in spot:
            #         with open(basename, "w") as bare_file:
            #             bare_file.write(wind_string)

            #     if 'csv_output' in spot:
            #         wind_csv = f"{wind_avg},{wind_gust},{wind_lull},{wind_dir},{wind_dir_degrees}"
            #         print(f"csv: {wind_csv}")
            #         with open(f"{basename}.csv", "w") as csv_file:
            #             csv_file.write(wind_csv)

            #     # Wind string with stamp
            #     if basename == "ventana_2":
            #         wind_string_with_stamp = f"{wind_dir} {wind_dir_degrees} {wind_avg}g{wind_gust} , 1.1f,2s,225_1.1f,2s,225 - {local_stamp}"
            #     else:
            #         wind_string_with_stamp = f"{wind_dir} {wind_dir_degrees} {wind_avg}g{wind_gust} , ._. - {local_stamp}"

            #     file_name = basename + ".txt"
            #     print(f"writing {file_name} : {wind_string_with_stamp}")
            #     with open(file_name, "w") as text_file:
            #         text_file.write(wind_string_with_stamp)

            #     # Generate JSON output
            #     print(f"writing {basename}.json")
            #     with open(basename +'.json', "w") as json_file:
            #         json_file.write(json.dumps(spot_data))

            #     # Run extra scripts if specified
            #     if extra_scripts:
            #         current_script_dir = os.path.dirname(os.path.abspath(__file__))
            #         for script in extra_scripts:
            #             script_path = os.path.join(current_script_dir, script)
            #             command = ["python3", script_path]
            #             result = subprocess.run(command, check=True, capture_output=True, text=True)

        except Exception as e:
            print(f"failed with spot {spot}")
            print(e)
    # Write out processed data to json file
    with open("ik_last_processed.json", "w") as json_file:
        json.dump(processed_data, json_file, indent=2)

    return processed_data

def main(spots_dict, debug=False):
    """Main function to run the entire process
    
    Args:
        spots_dict: Dictionary of spots to use (overrides spots_file if provided)
        
    Returns:
        Dictionary with processed data for each spot
    """

    if not debug:
        token = get_token()
        spots_dict = fetch_data(token, spots_dict)
        print(spots_dict)
    else:
        print("using debug datafile ik_last_fetched.json")
        with open("ik_last_fetch.json", "r") as json_file:
            spots_dict = json.load(json_file)

    if (spots_dict):
        return process_spots(spots_dict)
    else:
        return {}

# Only run if script is executed directly
if __name__ == "__main__":
    # Uncomment to specify scripts to run after fetching
    # extra_scripts = ['canarias.py','hoodriver.py','florida.py']
    main()
