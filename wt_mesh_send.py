"""
Based on Meshtastic MQTT Connect Version 0.7.1 by https://github.com/pdxlocations
Powered by Meshtastic™ https://meshtastic.org/
"""

import paho.mqtt.client as mqtt
from meshtastic import mesh_pb2, mqtt_pb2, portnums_pb2, telemetry_pb2
import random
import threading
import sqlite3
import time
from datetime import datetime
from time import mktime
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
import json
import re

#### Debug Options
debug = False
print_service_envelope = False
print_message_packet = False
print_text_message = False
print_node_info =  False
print_telemetry = False
print_failed_encryption_packet = False
print_position_report = False
color_text = True
display_encrypted_emoji = False
display_dm_emoji = False
display_private_dms = False

record_locations = False

### Default settings
mqtt_broker = "gt.wildc.net"
mqtt_port = 1884
mqtt_username = "windytron_publish"
mqtt_password = "$$titz*"
root_topic = "mesh/2/e/"
channel = "Windytron"

key = "wtaaaaaaaaaaaaaaaaaaaQ=="

node_name = '!A3251978'

# Convert hex to int and remove '!'
node_number = int(node_name.replace("!", ""), 16)

client_short_name = "WT"
client_long_name = "WindyTron Mesh Sender"
client_hw_model = 255
node_info_interval_minutes = 60

#################################
### Program variables

default_key = "1PG7OiApB1nwvP+rz05pAQ==" # AKA AQ==
broadcast_id = 4294967295


#################################
# Program Base Functions
    
def set_topic():
    if debug: print("set_topic")
    global subscribe_topic, publish_topic, node_number, node_name
    node_name = '!' + hex(node_number)[2:]
    subscribe_topic = root_topic + channel + "/#"
    publish_topic = root_topic + channel + "/" + node_name

def current_time():
    current_time_seconds = time.time()
    current_time_struct = time.localtime(current_time_seconds)
    current_time_str = time.strftime("%H:%M:%S", current_time_struct)
    return(current_time_str)

def xor_hash(data):
    result = 0
    for char in data:
        result ^= char
    return result

def generate_hash(name, key):
    replaced_key = key.replace('-', '+').replace('_', '/')
    key_bytes = base64.b64decode(replaced_key.encode('utf-8'))
    h_name = xor_hash(bytes(name, 'utf-8'))
    h_key = xor_hash(key_bytes)
    result = h_name ^ h_key
    return result

def get_short_name_by_id(user_id):
    try:
        table_name = sanitize_string(mqtt_broker) + "_" + sanitize_string(root_topic) + sanitize_string(channel) + "_nodeinfo"
        with sqlite3.connect(db_file_path) as db_connection:
            db_cursor = db_connection.cursor()
    
            # Convert the user_id to hex and prepend '!'
            hex_user_id = '!' + hex(user_id)[2:]

            # Fetch the short name based on the hex user ID
            result = db_cursor.execute(f'SELECT short_name FROM {table_name} WHERE user_id=?', (hex_user_id,)).fetchone()

            if result:
                return result[0]
            # If we don't find a user id in the db, ask for an id
            else:
                if user_id != broadcast_id:
                    if debug: print("didn't find user in db")
                    send_node_info(user_id)  # DM unknown user a nodeinfo with want_response
                return f"Unknown User ({hex_user_id})"
    
    except sqlite3.Error as e:
        print(f"SQLite error in get_short_name_by_id: {e}")
    
    finally:
        db_connection.close()

def sanitize_string(input_str):
    # Check if the string starts with a letter (a-z, A-Z) or an underscore (_)
    if not re.match(r'^[a-zA-Z_]', input_str):
        # If not, add "_"
        input_str = '_' + input_str

    # Replace special characters with underscores (for database tables)
    sanitized_str = re.sub(r'[^a-zA-Z0-9_]', '_', input_str)
    return sanitized_str

#################################
# Receive Messages

def on_message(client, userdata, msg):
    # if debug: print("on_message")
    se = mqtt_pb2.ServiceEnvelope()
    is_encrypted = False
    try:
        se.ParseFromString(msg.payload)
        if print_service_envelope:
            print ("")
            print ("Service Envelope:")
            print (se)
        mp = se.packet
        if print_message_packet: 
            print ("")
            print ("Message Packet:")
            print(mp)
    except Exception as e:
        print(f"*** ParseFromString: {str(e)}")
        return
    
    if mp.HasField("encrypted") and not mp.HasField("decoded"):
        decode_encrypted(mp)
        is_encrypted=True

    if mp.decoded.portnum == portnums_pb2.TEXT_MESSAGE_APP:
        text_payload = mp.decoded.payload.decode("utf-8")
        process_message(mp, text_payload, is_encrypted)
        # print(f"{text_payload}")
        
    elif mp.decoded.portnum == portnums_pb2.NODEINFO_APP:
        info = mesh_pb2.User()
        info.ParseFromString(mp.decoded.payload)
        maybe_store_nodeinfo_in_db(info)
        if print_node_info:
            print("")
            print("NodeInfo:")
            print(info)
        
    elif mp.decoded.portnum == portnums_pb2.POSITION_APP:
        pos = mesh_pb2.Position()
        pos.ParseFromString(mp.decoded.payload)
        if record_locations:
            maybe_store_position_in_db(getattr(mp, "from"), pos)

    elif mp.decoded.portnum == portnums_pb2.TELEMETRY_APP:
        env = telemetry_pb2.Telemetry()
        env.ParseFromString(mp.decoded.payload)

        # Device Metrics
        device_metrics_dict = {
            'Battery Level': env.device_metrics.battery_level,
            'Voltage': round(env.device_metrics.voltage, 2),
            'Channel Utilization': round(env.device_metrics.channel_utilization, 1),
            'Air Utilization': round(env.device_metrics.air_util_tx, 1)
        }
        # Environment Metrics
        environment_metrics_dict = {
            'Temp': round(env.environment_metrics.temperature, 2),
            'Humidity': round(env.environment_metrics.relative_humidity, 0),
            'Pressure': round(env.environment_metrics.barometric_pressure, 2),
            'Gas Resistance': round(env.environment_metrics.gas_resistance, 2)
        }
        # Power Metrics
            # TODO
        # Air Quality Metrics
            # TODO

        if print_telemetry: 

            device_metrics_string = "From: " + get_short_name_by_id(getattr(mp, "from")) + ", "
            environment_metrics_string = "From: " + get_short_name_by_id(getattr(mp, "from")) + ", "

            # Only use metrics that are non-zero
            has_device_metrics = True
            has_environment_metrics = True
            has_device_metrics = all(value != 0 for value in device_metrics_dict.values())
            has_environment_metrics = all(value != 0 for value in environment_metrics_dict.values())

            # Loop through the dictionary and append non-empty values to the string
            for label, value in device_metrics_dict.items():
                if value is not None:
                    device_metrics_string += f"{label}: {value}, "

            for label, value in environment_metrics_dict.items():
                if value is not None:
                    environment_metrics_string += f"{label}: {value}, "

            # Remove the trailing comma and space
            device_metrics_string = device_metrics_string.rstrip(", ")
            environment_metrics_string = environment_metrics_string.rstrip(", ")

            # Print or use the final string
            if has_device_metrics:
                print(device_metrics_string)
            if has_environment_metrics:
                print(environment_metrics_string)


def decode_encrypted(mp):
        
        try:
            # Convert key to bytes
            key_bytes = base64.b64decode(key.encode('ascii'))
      
            nonce_packet_id = getattr(mp, "id").to_bytes(8, "little")
            nonce_from_node = getattr(mp, "from").to_bytes(8, "little")

            # Put both parts into a single byte array.
            nonce = nonce_packet_id + nonce_from_node

            cipher = Cipher(algorithms.AES(key_bytes), modes.CTR(nonce), backend=default_backend())
            decryptor = cipher.decryptor()
            decrypted_bytes = decryptor.update(getattr(mp, "encrypted")) + decryptor.finalize()

            data = mesh_pb2.Data()
            data.ParseFromString(decrypted_bytes)
            mp.decoded.CopyFrom(data)

        except Exception as e:

            if print_message_packet: print(f"failed to decrypt: \n{mp}")
            if debug: print(f"*** Decryption failed: {str(e)}")
            return


def process_message(mp, text_payload, is_encrypted):
    if debug: print("process_message")
    if not message_exists(mp):
        from_node = getattr(mp, "from")
        to_node = getattr(mp, "to")
        sender_short_name = get_short_name_by_id(from_node)
        receiver_short_name = get_short_name_by_id(to_node)
        string = ""
        private_dm = False

        if to_node == node_number:
            string = f"{current_time()} DM from {sender_short_name}: {text_payload}"
            if display_dm_emoji: string = string[:9] + dm_emoji + string[9:]

        elif from_node == node_number and to_node != broadcast_id:
            string = f"{current_time()} DM to {receiver_short_name}: {text_payload}"
            
        elif from_node != node_number and to_node != broadcast_id:
            if display_private_dms:
                string = f"{current_time()} DM from {sender_short_name} to {receiver_short_name}: {text_payload}"
                if display_dm_emoji: string = string[:9] + dm_emoji + string[9:]
            else:
                if debug: print("Private DM Ignored")
                private_dm = True
            
        else:    
            string = f"{current_time()} {sender_short_name}: {text_payload}"

        if is_encrypted and not private_dm:
            color="encrypted"
            if display_encrypted_emoji: string = string[:9] + encrypted_emoji + string[9:]
        else:
            color="unencrypted"
        if not private_dm:
            update_gui(string, text_widget=message_history, tag=color)
        m_id = getattr(mp, "id")
        insert_message_to_db(current_time(), sender_short_name, text_payload, m_id, is_encrypted)

        text = {
            "message": text_payload,
            "from": getattr(mp, "from"),
            "id": getattr(mp, "id"),
            "to": getattr(mp, "to")
        }
        if print_text_message: 
            print("")
            print(text)
    else:
        if debug: print("duplicate message ignored")

# check for message id in db, ignore duplicates
def message_exists(mp):
    if debug: print("message_exists")
    try:
        table_name = sanitize_string(mqtt_broker) + "_" + sanitize_string(root_topic) + sanitize_string(channel) + "_messages"

        with sqlite3.connect(db_file_path) as db_connection:
            db_cursor = db_connection.cursor()

            # Check if a record with the same message_id already exists
            existing_record = db_cursor.execute(f'SELECT * FROM {table_name} WHERE message_id=?', (str(getattr(mp, "id")),)).fetchone()

            return existing_record is not None

    except sqlite3.Error as e:
        print(f"SQLite error in message_exists: {e}")

    finally:
        db_connection.close()

#################################
# Send Messages

def direct_message(destination_id):
    if debug: print("direct_message")
    destination_id = int(destination_id[1:], 16)
    publish_message(destination_id)


def publish_message(destination_id,message_text):
    global key
    if debug:
        # print("publish_message")
        # print(message_text)
        pass

    if message_text:
        encoded_message = mesh_pb2.Data()
        encoded_message.portnum = portnums_pb2.TEXT_MESSAGE_APP 
        encoded_message.payload = message_text.encode("utf-8")

    generate_mesh_packet(destination_id, encoded_message)


def generate_mesh_packet(destination_id, encoded_message):
    mesh_packet = mesh_pb2.MeshPacket()

    setattr(mesh_packet, "from", node_number)
    mesh_packet.id = random.getrandbits(32)
    mesh_packet.to = destination_id
    mesh_packet.want_ack = False
    mesh_packet.channel = generate_hash(channel, key)
    mesh_packet.hop_limit = 3

    if key == "":
        mesh_packet.decoded.CopyFrom(encoded_message)
        if debug: print("key is none")
    else:
        mesh_packet.encrypted = encrypt_message(channel, key, mesh_packet, encoded_message)
        if debug: print("key present")

    service_envelope = mqtt_pb2.ServiceEnvelope()
    service_envelope.packet.CopyFrom(mesh_packet)
    service_envelope.channel_id = channel
    service_envelope.gateway_id = node_name
    # print (service_envelope)

    payload = service_envelope.SerializeToString()
    set_topic()
    client.publish(topic=publish_topic, payload=payload,qos=0,retain=True)


def encrypt_message(channel, key, mesh_packet, encoded_message):
    if debug: print("encrypt_message")

    mesh_packet.channel = generate_hash(channel, key)
    key_bytes = base64.b64decode(key.encode('ascii'))

    # print (f"id = {mesh_packet.id}")
    nonce_packet_id = mesh_packet.id.to_bytes(8, "little")
    nonce_from_node = node_number.to_bytes(8, "little")
    # Put both parts into a single byte array.
    nonce = nonce_packet_id + nonce_from_node

    cipher = Cipher(algorithms.AES(key_bytes), modes.CTR(nonce), backend=default_backend())
    encryptor = cipher.encryptor()
    encrypted_bytes = encryptor.update(encoded_message.SerializeToString()) + encryptor.finalize()

    return encrypted_bytes


#################################
# MQTT Server 
    
def connect_mqtt():
    if debug: print("connect_mqtt")
    global mqtt_broker, mqtt_username, mqtt_password, root_topic, channel, node_number, db_file_path, key
    if not client.is_connected():
        try:

            if key == "AQ==":
                if debug: print("key is default, expanding to AES128")
                key = "1PG7OiApB1nwvP+rz05pAQ=="

            node_number = int(node_number)  # Convert the input to an integer

            padded_key = key.ljust(len(key) + ((4 - (len(key) % 4)) % 4), '=')
            replaced_key = padded_key.replace('-', '+').replace('_', '/')
            key = replaced_key

            if debug: print (f"padded & replaced key = {key}")

            client.username_pw_set(mqtt_username, mqtt_password)
            client.connect(mqtt_broker, mqtt_port, 60)
            client.loop_start()

        except Exception as e:
            print(f"{current_time()} >>> Failed to connect to MQTT broker: {str(e)}")


def disconnect_mqtt():
    if debug: print("disconnect_mqtt")
    if client.is_connected():
        client.loop_stop()
        client.disconnect()

def on_connect(client, userdata, flags, reason_code, properties):

    set_topic()
    
    if debug: print("on_connect")
    if debug: 
        if client.is_connected():
            print("client is connected")
    
    # if reason_code == 0:
    #     if debug: print(f"Subscribe Topic is: {subscribe_topic}")
    #     client.subscribe(subscribe_topic)
    

############################
# Main Threads

# Load file/channel pairs from JSON
with open('wt_mesh_files.json', 'r') as jf:
    file_channel_list = json.load(jf)

#client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, client_id="windytron_mesh_sender", clean_session=True, userdata=None)
client = mqtt.Client(client_id="WindyTronSender", clean_session=True, userdata=None)

client.on_connect = on_connect
client.on_message = on_message

connect_mqtt()

# both root topics because clients will force an old topic willy nilly when doing client proxy
for root_topic in ["mesh/2/c/", "mesh/2/e/"]:
    for channel, filepath in file_channel_list:
        if channel == "":
            channel = filepath.split('.')[0].title()
        try:
            with open(filepath, mode='r') as f:
                wind_string = f.read().strip()
                if " + " not in wind_string:
                    wind_string = wind_string.split('+')[0]
                print(f"{channel} : {wind_string}")
            publish_message(broadcast_id, wind_string)
        except Exception as e:
            print(f"Error reading {filepath} for channel {channel}: {e}")

disconnect_mqtt()
