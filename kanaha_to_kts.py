#!/usr/bin/python3
# Read kanaha file and output kanaha.kts

import os

#function to convert from mph to kts
def mph_to_kts(mph):
    try:
        return float(mph) * 0.868976
    except:
        return "xx"

#read in kanaha.json
in_file = open("kanaha", "r")
mph = in_file.read().rstrip()
print(mph)

#split mph on spaces
mph_list = mph.split(" ")

#split the last element of mph_list on 'g'
mph_list_vel = mph_list[-1].split("g")

avg = mph_to_kts(mph_list_vel[0])
gust = mph_to_kts(mph_list_vel[1])

output_string = "%s %dg%d kts" % (mph_list[0], avg, gust)

print(output_string)

#output to file named kanaha.kts
with open("kanaha.kts", "w") as text_file:
    text_file.write(output_string)



