import requests
import datetime

# URL of the weather forecast page
url = "https://forecast.weather.gov/shmrn.php?mz=gmz042&syn=gmz005"

# Fetch the page content
response = requests.get(url)
response.raise_for_status()  # Ensure the request was successful
html = response.text

keyword = "TODAY</font></strong>"

start_index = html.find(keyword)

if start_index != -1:
    # Extract the next lines until we find the next <strong> element
    end_index = html.find("<strong>", start_index + len(keyword))

    if end_index == -1:
        text = html[start_index + len(keyword) :].replace("&nbsp;","").replace("\n","")
    else:
        text = html[start_index + len(keyword) : end_index].replace("&nbsp;", "").replace("\n","")

    # maximum chars is about Northeast to east winds 20 to 25 knots. Seas 4to 6 feet, occasionally to 8 feet, except 5 to 7 feet,occasionally to 9 feet west of Cosgrove Shoal Light12345678910111213144
    
    # Create the JSON output
    output = '{"label":"Hawk Channel Today","type":"text","text": "' + text[0:170] + '"}'
    print(f"output size : {len(output)}")
    # Output to JSON file
    with open("hawk_channel_nws.json", "w") as f:
        f.write(output)

else:
    print(f"Target string {keyword} not found, trying AFTERNOON")
    
    keyword = "THIS AFTERNOON</font></strong>"
    start_index = html.find(keyword)

    if start_index != -1:
        # Extract the next lines until we find the next <strong> element
        end_index = html.find("<strong>", start_index + len(keyword))

        if end_index == -1:
            text = html[start_index + len(keyword) :].replace("&nbsp;","").replace("\n","")
        else:
            text = html[start_index + len(keyword) : end_index].replace("&nbsp;", "").replace("\n","")

        # maximum chars is about Northeast to east winds 20 to 25 knots. Seas 4to 6 feet, occasionally to 8 feet, except 5 to 7 feet,occasionally to 9 feet west of Cosgrove Shoal Light12345678910111213144
        
        # Create the JSON output
        output = '{"label":"Hawk Channel Afternoon","type":"text","text": "' + text[0:170] + '"}'
        print(f"output size : {len(output)}")
        # Output to JSON file
        with open("hawk_channel_nws.json", "w") as f:
            f.write(output)
    else:
        tomorrow = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%A').upper()
        print(f"{keyword} not found, trying for {tomorrow}")

        keyword = f"{tomorrow}</font></strong>"
        start_index = html.find(keyword)

        if start_index != -1:
            # Extract the next lines until we find the next <strong> element
            end_index = html.find("<strong>", start_index + len(keyword))

            if end_index == -1:
                text = html[start_index + len(keyword) :].replace("&nbsp;","").replace("\n","")
            else:
                text = html[start_index + len(keyword) : end_index].replace("&nbsp;", "").replace("\n","")

            # maximum chars is about Northeast to east winds 20 to 25 knots. Seas 4to 6 feet, occasionally to 8 feet, except 5 to 7 feet,occasionally to 9 feet west of Cosgrove Shoal Light12345678910111213144
            
            # Create the JSON output
            output = '{"label":"Hawk Channel '+tomorrow.title()+'","type":"text","text": "' + text[0:170] + '"}'
            print(f"output size : {len(output)}")
            # Output to JSON file
            with open("hawk_channel_nws.json", "w") as f:
                f.write(output)
        else:
            print(f"{keyword} not found")


