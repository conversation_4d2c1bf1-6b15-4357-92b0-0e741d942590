# station down generator
# generate error zeros
spot_name = "kanaha"
wind_dir = "N"
wind_dir_degrees = "0"
wind_avg = "XX"
wind_gust = "XX"
wind_lull = "XX"
wind_string = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_avg) + "g" + str(wind_gust)
#wind_kts = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(m2k(data[0])) + "g" + str(m2k(data[2]))
wind_csv = str(wind_avg) + "," + str(wind_gust) +"," + str(wind_lull)  + "," + str(wind_dir) + "," + str(wind_dir_degrees)
wind_lag =  str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_lull) + "-" + str(wind_avg) + "g" + str(wind_gust)
print (wind_string)
# 
# # need to output this : ENE 56 16g20
with open(spot_name, "w") as text_file:
    text_file.write(wind_string)

# with open(spot_name+"_kts", "w") as text_file:
#     text_file.write(wind_kts)

spot_csv = spot_name+".csv"
with open(spot_csv, "w") as text_file:
    text_file.write(wind_csv)
    
# output wind_lag to file
with open(spot_name+"_lag", "w") as text_file:
    text_file.write(wind_lag)