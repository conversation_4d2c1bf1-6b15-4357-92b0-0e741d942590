#!/usr/bin/perl
use strict;
use CGI qw(param);
use LWP::Simple;

my $buoy_id = param("buoy_id");
$buoy_id =~ s/[^ [:ascii:] ]//g;

print "buoy_id : " . $buoy_id;

my $url = "https://www.ndbc.noaa.gov/station_page.php?station=" . $buoy_id;
my $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
# print $rawrss;
chomp $rawrss;

$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Height is $height\n";

$rawrss =~ /Swell\sPeriod\s\(SwP\):\<\/td\>\<td\>\s+(\d+)\.?\d?\s+sec\s?\<\/td\>/;

my $period = $1;
$period .= "s";
$period =~ s/\s//;
$period =~ s/sec/s/;


print "Period is $period\n";

$rawrss =~ /Swell\sDirection\s\(SwD\):\<\/td\>\<td[^ENSW]*([ENSW]+).*+/;


my $direction = $1;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;
print "Data is ".$height.",". $period . "," . $direction . "\n";
unless ($height eq '') { # || $period eq '' || $direction eq '') {
	print "writing file";
    my $filename = $buoy_id . ".txt";
    open my $LOG, '>', $filename or die "Could not open file '$filename' for writing: $!";
    print $LOG $height.",". $period . "," . $direction . "\n";
    close $LOG;

    # run io updater curl.
    my $feed_name = $buoy_id . "-csv";
    my $csv = $height.",". $period . "," . $direction;
    my $curl_command = "curl -F 'value=$csv' -H 'X-AIO-Key: 0f0502cadd574ea390fe273d19971a12'   https://io.adafruit.com/api/v2/tavdog/feeds/buoy-data.$feed_name/data";
    print $curl_command;
    system($curl_command);
}

