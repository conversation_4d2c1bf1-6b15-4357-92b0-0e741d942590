#!/usr/bin/perl
use strict;
use CGI qw(param);
use LWP::Simple;
use JSON;
use POSIX qw(strftime);
use Time::Local;

my $buoy_id = param("buoy_id");
$buoy_id =~ s/[^ [:ascii:] ]//g;

print "buoy_id : " . $buoy_id . "\n";

# Extract just the buoy ID number from "buoy_id=51202" format
if ($buoy_id =~ /buoy_id=(\d+)/) {
    $buoy_id = $1;
    print "Extracted buoy ID: " . $buoy_id . "\n";
}

my $url = "https://www.ndbc.noaa.gov/station_page.php?station=" . $buoy_id;
my $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
# print $rawrss;
chomp $rawrss;

$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+)\s*ft\<\/td.*/;
my $height_ft = $1;
my $height = $height_ft . "f";
print "Height is $height\n";

$rawrss =~ /Swell\sPeriod\s\(SwP\):\<\/td\>\<td\>\s*(\d+)\.?\d?\s*sec\s?\<\/td\>/;
my $period_sec = $1;
my $period = $period_sec . "s";
print "Period is $period\n";

$rawrss =~ /Swell\sDirection\s\(SwD\):\<\/td\>\<td[^ENSW]*([ENSW]+).*+/;
my $direction = $1;
$direction =~ s/\(//g;
$direction =~ s/\s//g;
print "Direction is $direction\n";

# Create swell string in format like "3.3f,12s,SSE"
my $swell_string = $height . "," . $period . "," . $direction;
print "Swell string: $swell_string\n";
unless ($height eq '') {
    print "writing files\n";

    # Get current UTC timestamp
    my $utc_stamp = strftime("%Y-%m-%dT%H:%M:%S", gmtime());
    my $local_stamp = strftime("%Y-%m-%dT%H:%M:%S", localtime());

    # Create JSON data structure similar to ik_multi format
    my %spot_data = (
        "avg" => $height_ft + 0,  # Convert to number for swell height
        "gust" => $height_ft + 0, # Use same value for consistency
        "lull" => $height_ft + 0, # Use same value for consistency
        "dir_card" => $direction,
        "dir_deg" => 0,  # NOAA doesn't provide degrees, use 0
        "stamp" => $local_stamp,
        "utc_stamp" => $utc_stamp,
        "label" => "Buoy " . $buoy_id,
        "swell_string" => $swell_string,
        "wind_string" => $swell_string  # For compatibility
    );

    # Write JSON file (this is what the generalized system expects)
    my $json_filename = $buoy_id . ".json";
    open my $JSON_FILE, '>', $json_filename or die "Could not open file '$json_filename' for writing: $!";
    print $JSON_FILE encode_json(\%spot_data);
    close $JSON_FILE;
    print "Wrote JSON to $json_filename\n";

    # Write legacy text file for backward compatibility
    my $txt_filename = $buoy_id . ".txt";
    open my $TXT_FILE, '>', $txt_filename or die "Could not open file '$txt_filename' for writing: $!";
    print $TXT_FILE $swell_string . "\n";
    close $TXT_FILE;
    print "Wrote text to $txt_filename\n";
}

