#!/usr/bin/python3

import json
import requests
import os

{"station_id":7861,"station_name":"Gustaf Station","obs":[{"timestamp":1653631452,"wind_avg":2.1,"wind_direction":7,"wind_gust":3.6,"wind_lull":1.0,}]}
#function to convert degrees to cardinal direction
def deg_to_card(deg):
    if deg >= 348.75 or deg < 11.25:
        return "N"
    elif deg >= 11.25 and deg < 33.75:
        return "NNE"
    elif deg >= 33.75 and deg < 56.25:
        return "NE"
    elif deg >= 56.25 and deg < 78.75:
        return "ENE"
    elif deg >= 78.75 and deg < 101.25:
        return "E"
    elif deg >= 101.25 and deg < 123.75:
        return "ESE"
    elif deg >= 123.75 and deg < 146.25:
        return "SE"
    elif deg >= 146.25 and deg < 168.75:
        return "SSE"
    elif deg >= 168.75 and deg < 191.25:
        return "S"
    elif deg >= 191.25 and deg < 213.75:
        return "SSW"
    elif deg >= 213.75 and deg < 236.25:
        return "SW"
    elif deg >= 236.25 and deg < 258.75:
        return "WSW"
    elif deg >= 258.75 and deg < 281.25:
        return "W"
    elif deg >= 281.25 and deg < 303.75:
        return "WNW"
    elif deg >= 303.75 and deg < 326.25:
        return "NW"
    elif deg >= 326.25 and deg < 348.75:
        return "NNW"

#function to convert mph to kts
def mph_to_kts(mph):
    return mph * 0.868976
    
url = 'https://swd.weatherflow.com/swd/rest/observations/station/7861?token=0fb249a3-cad0-4c54-815f-32731ab5a708'
# get station data from weatherflow tempest station
r = requests.get(url)
data = r.json()

print(data)
if 'obs' in data:
    if len(data['obs']) > 0:
        print(data['obs'][0]['wind_avg'])
        print(data['obs'][0]['wind_direction'])
        print(data['obs'][0]['wind_gust'])

        data = data['obs'][0]
        #print(data['obs'][0])


    #print(data['wind_avg'])
    #NE 42 9g13 , 0.7f,9s,NNE30 - 2022-05-14T02:38:07
    # degrees symbol = °
    txt_string = "%s %s° %sg%s kts" % (deg_to_card(data['wind_direction']), int(data['wind_direction']), int(round(mph_to_kts(data['wind_avg']),0)), int(round(mph_to_kts(data['wind_gust']),0)))
    # write data to txt file
    print(txt_string)
    with open('gustaf_station.txt', 'w') as f:
        f.write(txt_string)

    txt_string = "%s %s %sg%s mph" % (deg_to_card(data['wind_direction']), int(data['wind_direction']), int(round(data['wind_avg'],0)), int(round(data['wind_gust'],0)))
    with open('gustaf_station', 'w') as f:
        f.write(txt_string)

    # execute system command
    #os.system("cd ~/wildc.net/wind ; perl valdevq.pl")

