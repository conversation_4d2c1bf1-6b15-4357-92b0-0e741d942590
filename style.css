/* Reset default styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}

header {
    background-color: #ffffff;
    color: white;
    padding: 10px 0;
    text-align: center;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 20px auto;
    background-color: white;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 24px;
    text-align: center;
    margin-bottom: 10px;
    background-color: #e6e6e6;
    padding: 10px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table,
th,
td {
    border: 1px solid #ddd;
}

th,
td {
    padding: 10px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

.float-right {
    float: right;
    margin-left: 20px;
}

.link {
    color: #0044cc;
    text-decoration: none;
}

.link:hover {
    text-decoration: underline;
}

.footer {
    text-align: center;
    padding: 20px;
    background-color: #0044cc;
    color: white;
}

@media only screen and (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .float-right {
        float: none;
        margin-left: 0;
        margin-bottom: 10px;
        text-align: center;
    }

    table {
        margin-bottom: 10px;
    }

    .section-title {
        font-size: 20px;
    }
}