import yaml
import json
import pprint
import importlib
import subprocess
from datetime import datetime, timezone

current_time = datetime.now(timezone.utc)
data = yaml.safe_load("wt.yaml")
with open("wt.yaml", "r") as f:
    data = yaml.safe_load(f)
# print(yaml.dump(data, default_flow_style=False))
# pprint.pprint(data['inputs'])

print(f"got input keys: {data['inputs'].keys()}")

# for each input populate the last stamp from the json file
for input_name, input_dict in data["inputs"].items():
    print(f"{input_name}.json : ", end='')
    try:
        with open(f"{input_name}.json", "r") as f:
            j = json.load(f)
            if "utc_stamp" in j:
                input_dict["utc_stamp"] = j["utc_stamp"]
                print(f"{input_dict['utc_stamp']}")
            else:
                print("none")
    except Exception as e:
        print(f"no file {e}")

    # Set the needs_update flags
    input_dict["needs_update"] = False
    # Get the interval in minutes (default to 5 if not specified)
    interval_min = input_dict.get("interval_min", 5)
    
    # Check if we need to update this input based on timestamp
    if "utc_stamp" in input_dict:
        try:
            # Parse the timestamp assuming UTC
            stamp = input_dict["utc_stamp"]
            last_update = datetime.fromisoformat(stamp).replace(tzinfo=timezone.utc)
            
            # Calculate time difference in minutes
            time_diff = (current_time - last_update).total_seconds() / 60
            # print(f"{input_name} time dif : {time_diff}")
            # Only include if older than interval_min
            input_dict["needs_update"] = time_diff >= interval_min
        except (ValueError, TypeError) as e:
            # If timestamp parsing fails, assume we need to update
            print(f"Error parsing timestamp '{input_dict.get('stamp', 'None')}': {e}")
            input_dict["needs_update"] = True


# for Each provider we iterate
for provider in data['providers'].keys():
    print(f"Doing provider {provider}")

    # Collect inputs for this provider that need updating
    provider_inputs = {}
    for input_name, input_dict in data["inputs"].items():
        if input_dict["provider"] == provider and input_dict["needs_update"]:
            provider_inputs[input_name] = input_dict

    if not provider_inputs:
        print(f"No inputs need updating for provider {provider}")
        continue

    # Process inputs based on provider configuration
    results = {}
    provider_config = data['providers'][provider]

    if 'module' in provider_config and 'caller' in provider_config:
        # Module-based provider (like weatherflow)
        try:
            module_name = provider_config['module']
            caller_name = provider_config['caller']

            # Import the module dynamically
            module = importlib.import_module(module_name)
            caller_func = getattr(module, caller_name)

            # Prepare arguments based on configuration
            kwargs = {}
            if 'args' in provider_config:
                for arg in provider_config['args']:
                    if arg == 'spots_dict':
                        kwargs['spots_dict'] = provider_inputs
                    elif arg == 'debug':
                        kwargs['debug'] = True
                    # Add more argument mappings as needed

            # Call the function
            results = caller_func(**kwargs)

        except Exception as e:
            print(f"Error calling {provider} module: {e}")
            continue

    elif 'command' in provider_config and 'script' in provider_config:
        # Command-based provider (like weatherflow_timer, noaa_ndbc)
        try:
            command = provider_config['command']
            script = provider_config['script']

            # Process each input individually for command-based providers
            for input_name, input_dict in provider_inputs.items():
                print(f"Processing {input_name} with {provider}")

                # Build command arguments
                cmd_args = [command, script]

                if 'args' in provider_config:
                    for arg_template in provider_config['args']:
                        # Replace placeholders in arguments
                        arg = arg_template.format(
                            id=input_dict.get('id', ''),
                            name=input_name
                        )
                        cmd_args.append(arg)

                # Execute the command
                try:
                    result = subprocess.run(
                        cmd_args,
                        capture_output=True,
                        text=True,
                        check=True,
                        cwd='.'
                    )
                    print(f"Command output for {input_name}: {result.stdout[:100]}...")

                    # For command-based providers, we expect them to write their own JSON files
                    # Try to read the output file
                    try:
                        with open(f"{input_name}.json", "r") as f:
                            spot_data = json.load(f)
                            results[input_name] = spot_data
                    except FileNotFoundError:
                        print(f"Warning: Expected output file {input_name}.json not found")

                except subprocess.CalledProcessError as e:
                    print(f"Error running command for {input_name}: {e}")
                    print(f"stderr: {e.stderr}")

        except Exception as e:
            print(f"Error processing command-based provider {provider}: {e}")
            continue

    else:
        print(f"Invalid provider configuration for {provider}")
        continue

    # Write out json files for each spot and update data structure
    for name, spot_data in results.items():
        with open(f"{name}.json", "w") as f:
            json.dump(spot_data, f, indent=2)

    # Update the main data structure with results
    for key in results:
        print(f"doing {key}")
        if key in data['inputs']:
            data['inputs'][key]["data"] = results[key]
            if data['inputs'][key]["data"]["utc_stamp"] != data['inputs'][key]["utc_stamp"]:
                print(f"setting has_new_data for {key}")
                data['inputs'][key]['has_new_data'] = True
            else:
                print(f"stale data for {key}")
        else:
            print(f"Warning: Result key '{key}' not found in inputs")



