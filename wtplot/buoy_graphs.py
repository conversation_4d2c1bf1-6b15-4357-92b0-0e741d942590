# Graph yesterday and today wind data from CSV feed on adafruit io

import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import pytz
import requests
import matplotlib.pyplot as plt
import seaborn as sns
import re


def get_dataframe(username, key, feed, start, end):
    url = (
        # "https://io.adafruit.com/api/v2/%s/feeds/%s/data?start_time=%s&end_time=%s&limit=500"
        "https://io.adafruit.com/api/v2/%s/feeds/%s/data?limit=500"
        % (username, feed)
    )
    print("url:", url)

    response = requests.get(url)
    # print(response.json())
    if response.status_code == 200:
        response_dict = response.json()
    else:
        print(f"Error: {response.status_code}")
        return pd.DataFrame()  # return empty dataframe
    # Convert the data into a DataFrame
    # return response.json()
    # Convert to DataFrame and parse values
    df = pd.DataFrame(response.json())
    print(f"got {len(df)} records")
    if len(df) == 0:
        return None

    # Split into at most 6 columns
    split_columns = df["value"].str.split(",", expand=True, n=4)

    df[["height", "period", "direction", "steepness"]] = split_columns.iloc[:, :4]

    # Convert columns to numeric with 'coerce' to handle non-numeric values, and drop NaN rows

    df["height"] = pd.to_numeric(df["height"].str.replace(r'[^\d.]', '', regex=True), errors='coerce')
    df["period"] = pd.to_numeric(df["period"].str.replace(r'[^\d.]', '', regex=True), errors='coerce')

    # Drop rows with NaN values in any column
    # df = df.dropna(subset=["height", "period", "direction"])
    df = df.dropna(subset=["height", "period","direction","steepness"])

    # Convert created_at to datetime and convert to local timezone
    df["created_at"] = pd.to_datetime(
        df["created_at"], format="%Y-%m-%dT%H:%M:%SZ"
    ).dt.tz_localize("utc")
    df["created_at"] = df["created_at"].dt.tz_convert("Pacific/Honolulu")
    
    df["time_of_day"] = df["created_at"].dt.time

    # return df.sort_values(by="time_of_day")
    return df


START_HOUR = 0
END_HOUR = 23
# Get the current time in the Pacific/Honolulu timezone
now_local = datetime.now(pytz.timezone("Pacific/Honolulu"))

yesterday_start_local = now_local.replace(
    hour=START_HOUR, minute=0, second=0, microsecond=0
) - timedelta(days=1)

yesterday_start_utc = yesterday_start_local.astimezone(pytz.utc)
yesterday_end_zulu = (
    (yesterday_start_utc + timedelta(hours=END_HOUR - START_HOUR))
    .isoformat()
    .replace("+00:00", "Z")
)
yesterday_start_zulu = yesterday_start_utc.isoformat().replace("+00:00", "Z")

today_start_zulu = (
    now_local.replace(hour=START_HOUR, minute=0, second=0, microsecond=0)
    .astimezone(pytz.utc)
    .isoformat()
    .replace("+00:00", "Z")
)
# today_end_zulu = now_local.astimezone(pytz.utc).isoformat().replace("+00:00", "Z")
today_end_zulu = (
    (yesterday_start_utc + timedelta(hours=24+(END_HOUR - START_HOUR)))
    .isoformat()
    .replace("+00:00", "Z")
)
# print(f"now local {now_local}")
# print(f"yes local {yesterday_start_local}")
# print(f"today start z {today_start_zulu}")
# print(f"today end z {today_end_zulu}")
# print(f"yes start z {yesterday_start_zulu}")
# print(f"yes end z {yesterday_end_zulu}")

# Print the results
#  $ curl -H "X-AIO-Key: {io_key}"
# "https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z"
username = "tavdog"
key = "0f0502cadd574ea390fe273d19971a12"

stations = ["pauwela","lanai"]
# stations = ["hookipa"]

for station in stations:
        feed = f"buoy-data.{station}-csv"
        print(f"doing {feed}")
        # https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z
        df = get_dataframe(username, key, feed, yesterday_start_zulu, today_end_zulu)
        print(df["height"])
        # Create color palettes
        colors_today = sns.light_palette("navy", 3)
        # 3 different colors for today's height, direction, period
        colors_yesterday = (
            colors_today  # sns.dark_palette("navy", 3)  # Lighter versions for yesterday
        )
        # print(df["time_of_day"])
        # print(df_today["time_of_day"])

        # Create a time index (adjust as needed)
        now_local.strftime("%Y-%m-%dT")

        periods = (END_HOUR - START_HOUR) * 2
        time_index = pd.date_range(
            start=now_local.strftime("%Y-%m-%dT") + str(START_HOUR) + ":00:00",
            periods=periods,
            freq="5min",
        )
        # time_index = pd.date_range(start='2024-08-10T'+str(START_HOUR)+':00:00', periods=len(df), freq='5min')

        # Plot the data
        fig = go.Figure()

        if df is not None:
            time_index = df['created_at']

            # # Add a horizontal reference line at y=4.5 (45 directionrees NE)
            # fig.add_shape(
            #     type="line",
            #     x0=min(time_index),  # Extend the line across the entire x-axis range
            #     y0=4.5,
            #     x1=max(time_index),
            #     y1=4.5,
            #     line=dict(
            #         color="#967193",
            #         width=2,
            #         dash="dashdot",
            #     ),
            # )

            # Add a label to the reference line
            # fig.add_annotation(
            #     x=max(time_index),
            #     y=4.5,
            #     text="NE 45°",
            #     showarrow=False,
            #     font=dict(size=20, color="#967193"),
            #     xanchor="right",
            #     yanchor="top",
            # )
            # fig.add_shape(
            #     type="line",
            #     x0=min(time_index),  # Extend the line across the entire x-axis range
            #     y0=6.5,
            #     x1=max(time_index),
            #     y1=6.5,
            #     line=dict(
            #         color="#967193",
            #         width=2,
            #         dash="dashdot",
            #     ),
            # )

            # # Add a label to the reference line
            # fig.add_annotation(
            #     x=max(time_index),
            #     y=6.5,
            #     text="ENE 65°",
            #     showarrow=False,
            #     font=dict(size=20, color="#967193"),
            #     xanchor="right",
            #     yanchor="bottom",
            # )
            # if station == "kihei":
            #     # Add a horizontal reference line at y=3.1
            #     fig.add_shape(
            #         type="line",
            #         x0=min(time_index),  # Extend the line across the entire x-axis range
            #         y0=-3.1,
            #         x1=max(time_index),
            #         y1=-3.1,
            #         line=dict(
            #             color="#967193",
            #             width=2,
            #             dash="dashdot",
            #         ),
            #     )

            #     # Add a label to the reference line
            #     fig.add_annotation(
            #         x=max(time_index),
            #         y=-3.1,
            #         text="NW 315°",
            #         showarrow=False,
            #         font=dict(size=12, color="#967193"),
            #         xanchor="right",
            #         yanchor="top",
            #     )

            # Yesterday's Period
            fig.add_trace(
                go.Scatter(
                    # x=df["time_of_day"],
                    x=time_index,
                    y=df["period"],
                    # mode="lines",
                    mode="lines+text",
                    text=df['direction'],
                    textposition="top center",

                    name="Period",
                    line=dict(
                        # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
                        color="#fcbb60"
                    ),
                    # fillcolor="rgba(68, 68, 68, 0.1)",
                    # showlegend=False,
                )
            )

            # Yesterday's average
            fig.add_trace(
                go.Scatter(
                    # x=df["time_of_day"],
                    x=time_index,
                    y=df["height"],
                    mode="lines",
                    name="Height",
                    # line=dict(
                    #     # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
                    #     color="#fc0060"
                    # ),
                    # fillcolor="rgba(252, 153, 96, 0.3)",
                    # fill="tonexty",
                )
            )
            # Yesterday's Direction
            # fig.add_trace(
            #     go.Scatter(
            #         # x=df["time_of_day"],
            #         x=time_index,
            #         y=df["direction"],
            #         mode="lines",
            #         # name="Direction Yesterday",
            #         line=dict(
            #             # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            #             # color="#fc9960"
            #             color="rgba(0, 0, 0, 0)"
            #         ),
            #         fillcolor="rgba(252, 153, 96, 0.3)",
            #         fill="tonexty",
            #         showlegend=False,
            #     )
            # )
            # # Yesterday's direction
            # fig.add_trace(
            #     go.Scatter(
            #         # x=df["time_of_day"],
            #         x=time_index,
            #         y=df["direction"],
            #         mode="markers",
            #         name="Direction Yesterday",
            #         line=dict(
            #             # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            #             color="rgba(252, 153, 96, 0.5)"
            #         ),
            #     )
            # )

        # time_index = df_today['created_at'] - timedelta(days=1)
        # # Today's Period
        # fig.add_trace(
        #     go.Scatter(
        #         # x=df_today["time_of_day"],
        #         x=time_index,
        #         y=df_today["period"],
        #         mode="lines",
        #         name="Period Today",
        #         line=dict(
        #             # color=f"rgba({colors_today[0][0]*255}, {colors_today[0][1]*255}, {colors_today[0][2]*255})"
        #             color="rgba(0, 0, 0, 0)"
        #         ),
        #         showlegend=False,
        #     )
        # )
        # # Today's average
        # fig.add_trace(
        #     go.Scatter(
        #         # x=df_today["time_of_day"],
        #         x=time_index,
        #         y=df_today["height"],
        #         mode="lines",
        #         name="Velocity Today",
        #         line=dict(
        #             # color=f"rgba({colors_today[0][0]*255}, {colors_today[0][1]*255}, {colors_today[0][2]*255})"
        #             color="#0328fc"
        #         ),
        #         fillcolor="rgba(3, 40, 252, 0.1)",
        #         fill="tonexty",
        #     )
        # )
        # #Today's Direction
        # fig.add_trace(
        #     go.Scatter(
        #         # x=df["time_of_day"],
        #         x=time_index,
        #         y=df["direction"],
        #         mode="points",
        #         name="Direction Today",
        #         line=dict(
        #             # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
        #             color="rgba(3, 40, 252, 0.1)"
        #         ),
        #         # fillcolor="rgba(3, 40, 252, 0.1)",
        #         # fill="tonexty",
        #         showlegend=False,
        #     )
        # )

        # # Today's Direction
        # fig.add_trace(
        #     go.Scatter(
        #         # x=df["time_of_day"],
        #         x=time_index,
        #         y=df["direction"],
        #         mode="markers",
        #         name="Direction Today",
        #         line=dict(
        #             # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
        #             color="rgba(3, 40, 252, 0.5)"
        #         ),
        #     )
        # )


        all_y_values = []

        # # Loop through each trace to collect all y-values
        # for trace in fig.data:
        #     all_y_values.extend(trace["y"])  # Add y-values to the list

        # # Convert to numpy array for easier calculation (optional)
        # all_y_values = np.array(all_y_values)

        # Calculate min and max
        y_min = 0
        y_max = np.max(df['period'])
        x_len = len(time_index)
        # invisiable plot to to force dislay of right side y axis labels
        fig.add_trace(
            go.Scatter(
                # x= time_index,
                x=time_index[x_len : x_len + 1],
                # y= [min(min(df['period']),min(df['period'])),max(max(df['direction']),max(df['direction']))],
                y=[y_min, y_max],
                line=dict(
                    # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
                    color="rgba(0, 0, 0, 0)"
                ),
                yaxis="y2",
                showlegend=False,
            )
        )

        # Update layout
        fig.update_layout(
            title=f"{station}: Swell last 24 hours",
            xaxis_title="Time",
            yaxis_title="Swell Height",
            legend_title="Legend",
            xaxis=dict(
                title_font=dict(size=24),  # Adjust the size as needed
                tickfont=dict(size=24),
            ),
            yaxis=dict(
                title_font=dict(size=24),  # Adjust the size as needed
                tickfont=dict(size=24),
                range=[y_min - 1, y_max+1],
            ),
            yaxis2=dict(
                title_font=dict(size=24),  # Adjust the size as needed
                tickfont=dict(size=24),
                title="Period",
                anchor="free",
                overlaying="y",
                side="right",
                position=1,
               range = [min(df["period"]),max(df["period"])]
            ),
        )
        yaxis_range = fig["layout"]["yaxis"]["range"]

        # fig.update_layout(yaxis2=dict(range=yaxis_range))

        # Show the plot
        # fig.show()

        fig.write_image(
            f"/home/<USER>/windytron.com/{station}_delta.png", width=2048, height=900
        )

        fig.write_html(f"/home/<USER>/windytron.com/{station}_delta.html")
