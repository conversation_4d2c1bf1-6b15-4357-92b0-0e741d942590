import plotly.graph_objects as go
import pandas as pd

# Sample data (replace with your actual data)
yesterday_data = [
    {"value": "20,25"},
    {"value": "18,22"},
    # ... more data points
]
today_data = [
    {"value": "22,28"},
    {"value": "20,26"},
    # ... more data points
]

# Convert data to pandas DataFrames
yesterday_df = pd.DataFrame(yesterday_data)
today_df = pd.DataFrame(today_data)

# Extract wind speed values
yesterday_wind = yesterday_df["value"].str.split(",", expand=True)[0].astype(int)
today_wind = today_df["value"].str.split(",", expand=True)[0].astype(int)

# Create a time index (adjust as needed)
time_index = pd.date_range(start="2024-08-10", periods=len(yesterday_wind), freq="H")

# Create the Plotly figure
fig = go.Figure()

# Add traces for yesterday and today's wind data
fig.add_trace(
    go.Scatter(x=time_index, y=yesterday_wind, mode="lines", name="Yesterday")
)
fig.add_trace(go.Scatter(x=time_index, y=today_wind, mode="lines", name="Today"))

# Customize the plot (optional)
fig.update_layout(
    title="Wind Speed Comparison",
    xaxis_title="Time",
    yaxis_title="Wind Speed (mph)",
    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
)

# Display the plot
fig.show()
