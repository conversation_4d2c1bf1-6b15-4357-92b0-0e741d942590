import plotly.graph_objects as go
import seaborn as sns
from datetime import datetime, timedelta
import pytz


def parse_data(data):
    timestamps = []
    avg_values = []
    gust_values = []
    lull_values = []
    for entry in data:
        created_at = datetime.fromisoformat(entry["created_at"].replace("Z", "+00:00"))
        created_at = created_at.astimezone(pytz.timezone("Pacific/Honolulu"))
        created_at.replace(day=1)
        # epoch = int(created_at.timestamp())

        avg, gust, lull = map(float, entry["value"].split(",")[0:3])

        timestamps.append(created_at)
        avg_values.append(avg)
        gust_values.append(gust)
        lull_values.append(lull)

    return timestamps, avg_values, gust_values, lull_values


# Sample data
data_today = [
    {"id": "1", "value": "24,34,17,ENE,77", "created_at": "2024-08-11T17:40:15Z"},
    {"id": "1", "value": "22,24,7,ENE,77", "created_at": "2024-08-11T18:49:15Z"},
]
data_yesterday = [
    {"id": "1", "value": "24,34,17,ENE,77", "created_at": "2024-08-10T17:46:15Z"},
    {"id": "2", "value": "26,36,19,ENE,78", "created_at": "2024-08-10T18:45:15Z"},
]

# Parse the data
timestamps, avg_values, gust_values, lull_values = parse_data(data)

# Define today and yesterday
today = datetime.now(pytz.timezone("Pacific/Honolulu")).date()
yesterday = today - timedelta(days=1)

# Separate today's and yesterday's data
timestamps_today = []
avg_today = []
gust_today = []
lull_today = []

timestamps_yesterday = []
avg_yesterday = []
gust_yesterday = []
lull_yesterday = []

for i in range(len(timestamps)):
    date = i
    # date = datetime.fromtimestamp(timestamps[i], pytz.timezone("Pacific/Honolulu")).date()

    if date == today:
        timestamps_today.append(timestamps[i])
        avg_today.append(avg_values[i])
        gust_today.append(gust_values[i])
        lull_today.append(lull_values[i])
    elif date == yesterday:
        timestamps_yesterday.append(timestamps[i])
        avg_yesterday.append(avg_values[i])
        gust_yesterday.append(gust_values[i])
        lull_yesterday.append(lull_values[i])

# Create color palettes
colors_today = sns.color_palette("husl", 3)
colors_yesterday = sns.light_palette("navy", 3)

# Plot the data
fig = go.Figure()

# Today's data
fig.add_trace(
    go.Scatter(
        x=timestamps_today,
        y=avg_today,
        mode="lines+markers",
        name="Avg Today",
        line=dict(
            color=f"rgba({colors_today[0][0]*255}, {colors_today[0][1]*255}, {colors_today[0][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=timestamps_today,
        y=gust_today,
        mode="lines+markers",
        name="Gust Today",
        line=dict(
            color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=timestamps_today,
        y=lull_today,
        mode="lines+markers",
        name="Lull Today",
        line=dict(
            color=f"rgba({colors_today[2][0]*255}, {colors_today[2][1]*255}, {colors_today[2][2]*255})"
        ),
    )
)

# Yesterday's data
fig.add_trace(
    go.Scatter(
        x=timestamps_yesterday,
        y=avg_yesterday,
        mode="lines+markers",
        name="Avg Yesterday",
        line=dict(
            color=f"rgba({colors_yesterday[0][0]*255}, {colors_yesterday[0][1]*255}, {colors_yesterday[0][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=timestamps_yesterday,
        y=gust_yesterday,
        mode="lines",
        name="Gust Yesterday",
        line=dict(
            color=f"rgba({colors_yesterday[1][0]*255}, {colors_yesterday[1][1]*255}, {colors_yesterday[1][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=timestamps_yesterday,
        y=lull_yesterday,
        mode="lines",
        name="Lull Yesterday",
        line=dict(
            color=f"rgba({colors_yesterday[2][0]*255}, {colors_yesterday[2][1]*255}, {colors_yesterday[2][2]*255})"
        ),
    )
)

# Update layout
fig.update_layout(
    title="Wind Data: Today vs Yesterday",
    xaxis_title="Time (Epoch Seconds)",
    yaxis_title="Values",
    legend_title="Legend",
    xaxis=dict(
        tickformat="%H:%M:%S",
        title_font=dict(size=14),
        tickfont=dict(size=12),
        # tickvals=[timestamps_today[0], timestamps_today[-1]],
        # ticktext=[
        #     datetime.fromtimestamp(timestamps_today[0]).strftime("%H:%M:%S"),
        #     datetime.fromtimestamp(timestamps_today[-1]).strftime("%H:%M:%S"),
        # ],
    ),
)

# Show the plot
fig.show()
