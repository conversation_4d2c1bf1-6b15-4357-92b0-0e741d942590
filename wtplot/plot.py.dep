import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import pytz
import requests
import matplotlib.pyplot as plt
import seaborn as sns

# Get the current time in the Pacific/Honolulu timezone
now_local = datetime.now(pytz.timezone("Pacific/Honolulu"))
yesterday_start_local = now_local.replace(
    hour=0, minute=0, second=0, microsecond=0
) - timedelta(days=1)
yesterday_start_utc = yesterday_start_local.astimezone(pytz.utc)
now_utc = now_local.replace(second=0, microsecond=0).astimezone(pytz.utc)

# Format the timestamps in ISO 8601 format with Zulu (Z) suffix
yesterday_start_zulu = yesterday_start_utc.isoformat().replace("+00:00", "Z")
now_utc_zulu = now_utc.isoformat().replace("+00:00", "Z")

# Print the results
# print("Start of Yesterday in Zulu Time:", yesterday_start_zulu)
# print("Current Time in UTC (Zulu Time):", now_utc_zulu)
# exit()
#  $ curl -H "X-AIO-Key: {io_key}"
# "https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z"
username = "tavdog"
key = "0f0502cadd574ea390fe273d19971a12"
feeds = ["kanaha-average", "kanaha-gust", "kanaha-lull", "kanaha-dir-deg"]
# https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z


url_avg = (
    "https://io.adafruit.com/api/v2/%s/feeds/wind-data.%s/data/chart?start_time=%s&end_time=%s"
    % (username, feeds[0], yesterday_start_zulu, now_utc_zulu)
)
print("url:", url_avg)

response = requests.get(url_avg)
if response.status_code == 200:
    response_dict = response.json()
else:
    print(f"Error: {response.status_code}")

# now we want to split the
# Convert the data into a DataFrame
data_avg = response_dict["data"]
# print(data_avg)
df = pd.DataFrame(data_avg, columns=["Timestamp", "Value"])

# Convert the 'Timestamp' column to datetime
df["Timestamp"] = pd.to_datetime(df["Timestamp"])
df["Timestamp"] = df["Timestamp"].dt.tz_convert(pytz.timezone("Pacific/Honolulu"))

# Extract the date and time parts
df["Date"] = df["Timestamp"].dt.date
df["Time"] = df["Timestamp"].dt.time

# Convert 'Value' column to float
df["Value"] = df["Value"].astype(float)

# Pivot the data_avg to align times for different dates
df_pivot = df.pivot(index="Time", columns="Date", values="Value")

# Generate a color scale with shades of blue
# Generate a color palette with seaborn
n_days = len(df_pivot.columns)
palette = sns.color_palette("Reds", n_colors=n_days)


# Convert seaborn colors to Plotly-compatible format
plotly_colors = []
for color in palette:
    r, g, b, *rest = color
    if rest:
        a = rest[0]
    else:
        a = 1  # Default alpha value if not provided
    plotly_colors.append(f"rgba({int(r * 255)}, {int(g * 255)}, {int(b * 255)}, {a})")

# Create a plotly figure
fig = go.Figure()

# Plot each day on the same graph
name = "Yesterday Avg"
for i, date in enumerate(df_pivot.columns):
    fig.add_trace(
        go.Scatter(
            x=df_pivot.index,
            y=df_pivot[date],
            mode="lines",
            connectgaps=True,
            name=name,
            line=dict(color=plotly_colors[i]),  # Apply the color
        )
    )
    name = "Today Avg"
fig.show()
url_gust = (
    "https://io.adafruit.com/api/v2/%s/feeds/wind-data.%s/data/chart?start_time=%s&end_time=%s"
    % (username, feeds[1], yesterday_start_zulu, now_utc_zulu)
)
print("url:", url_gust)

response = requests.get(url_gust)
if response.status_code == 200:
    response_dict = response.json()
else:
    print(f"Error: {response.status_code}")

# now we want to split the
# Convert the data into a DataFrame
data_gust = response_dict["data"]
# print(data_avg)
df = pd.DataFrame(data_gust, columns=["Timestamp", "Value"])

# Convert the 'Timestamp' column to datetime
df["Timestamp"] = pd.to_datetime(df["Timestamp"])
df["Timestamp"] = df["Timestamp"].dt.tz_convert(pytz.timezone("Pacific/Honolulu"))

# Extract the date and time parts
df["Date"] = df["Timestamp"].dt.date
df["Time"] = df["Timestamp"].dt.time

# Convert 'Value' column to float
df["Value"] = df["Value"].astype(float)

# Pivot the data_avg to align times for different dates
df_pivot = df.pivot(index="Time", columns="Date", values="Value")

# Generate a color scale with shades of blue
# Generate a color palette with seaborn
n_days = len(df_pivot.columns)
palette = sns.color_palette("Greens", n_colors=n_days)


# Convert seaborn colors to Plotly-compatible format
plotly_colors = []
for color in palette:
    r, g, b, *rest = color
    if rest:
        a = rest[0]
    else:
        a = 1  # Default alpha value if not provided
    plotly_colors.append(f"rgba({int(r * 255)}, {int(g * 255)}, {int(b * 255)}, {a})")

#fig = go.Figure()

# Plot each day on the same graph
name = "Yesterday Gust"
for i, date in enumerate(df_pivot.columns):
    fig.add_trace(
        go.Scatter(
            x=df_pivot.index,
            y=df_pivot[date],
            mode="lines",
            connectgaps=True,
            name=name,
            line=dict(color=plotly_colors[i]),  # Apply the color
        )
    )
    name = "Today Gust"

# Show the plot
fig.show()
# fig.write_image("fig1.png", width=1024, height=768, scale=1)
exit()


np.random.seed(1)

N = 100
x = np.random.rand(N)
y = np.random.rand(N)
colors = np.random.rand(N)
sz = np.random.rand(N) * 30

fig = go.Figure()
# Plot each day on the same graph using lines
for date in df_pivot.columns:
    fig.add_trace(
        go.Scatter(
            x=df_pivot.index,
            y=df_pivot[date],
            mode="lines",  # Use 'lines' mode for a line chart
            name=str(date),
        )
    )
fig.update_layout(
    title="Overlay of Two Days' Data",
    xaxis_title="Time of Day (HH:MM:SS)",
    yaxis_title="Value",
    xaxis=dict(title="Time of Day", tickformat="%H:%M:%S"),
    yaxis=dict(title="Values"),
    legend_title="Date",
)
# fig.show()
fig.write_image("fig1.png")
