# Graph yesterday and today wind data from CSV feed on adafruit io

import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import pytz
import requests
import matplotlib.pyplot as plt
import seaborn as sns

START_HOUR = 6
END_HOUR = 18
# Get the current time in the Pacific/Honolulu timezone
now_local = datetime.now(pytz.timezone("Pacific/Honolulu"))

yesterday_start_local = now_local.replace(
    hour=START_HOUR, minute=0, second=0, microsecond=0
) - timedelta(days=1)

yesterday_start_utc = yesterday_start_local.astimezone(pytz.utc)
yesterday_end_zulu = (
    (yesterday_start_utc + timedelta(hours=END_HOUR-START_HOUR)).isoformat().replace("+00:00", "Z")
)
yesterday_start_zulu = yesterday_start_utc.isoformat().replace("+00:00", "Z")
today_start_zulu = (
    now_local.replace(hour=START_HOUR, minute=0, second=0, microsecond=0)
    .astimezone(pytz.utc)
    .isoformat()
    .replace("+00:00", "Z")
)
today_end_zulu = now_local.astimezone(pytz.utc).isoformat().replace("+00:00", "Z")
print(f"now local {now_local}")
print(f"yes local {yesterday_start_local}")
print(f"today start z {today_start_zulu}")
print(f"today end z {today_end_zulu}")
print(f"yes start z {yesterday_start_zulu}")
print(f"yes end z {yesterday_end_zulu}")

# Print the results
#  $ curl -H "X-AIO-Key: {io_key}"
# "https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z"
username = "tavdog"
key = "0f0502cadd574ea390fe273d19971a12"
feed = "wind-data.kanaha-csv"
# https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z


def get_dataframe(start, end):
    url = (
        "https://io.adafruit.com/api/v2/%s/feeds/%s/data?start_time=%s&end_time=%s&limit=500"
        % (username, feed, start, end)
    )
    print("url:", url)

    response = requests.get(url)
    # print(response.json())
    if response.status_code == 200:
        response_dict = response.json()
    else:
        print(f"Error: {response.status_code}")
        return None
    # Convert the data into a DataFrame
    # return response.json()
    # Convert to DataFrame and parse values
    df = pd.DataFrame(response.json())
    print(f"got {len(df)} records")
    df[["avg", "gust", "lull", "dir", "deg"]] = df["value"].str.split(",", expand=True)
    df["avg"] = df["avg"].astype(float)
    df["gust"] = df["gust"].astype(float)
    df["lull"] = df["lull"].astype(float)
    df["deg"] = df["deg"].astype(float)/10


    # Convert created_at to datetime and convert to local timezone
    df["created_at"] = pd.to_datetime(df["created_at"], format="%Y-%m-%dT%H:%M:%SZ").dt.tz_localize('utc')
    df["created_at"] = df["created_at"].dt.tz_convert("Pacific/Honolulu")
    df["created_at"] = df["created_at"].dt.time
    df["time_of_day"] = df["created_at"]

    return df.sort_values(by="time_of_day")


df_yesterday = get_dataframe(yesterday_start_zulu, yesterday_end_zulu)
df_today = get_dataframe(today_start_zulu, today_end_zulu)
# print(data)

# if data == None:
#     print("data error")
#     exit(1)


# Create color palettes
colors_today = sns.light_palette("navy", 3)
# 3 different colors for today's avg, gust, lull
colors_yesterday = colors_today # sns.dark_palette("navy", 3)  # Lighter versions for yesterday
# print(df_yesterday["time_of_day"])
# print(df_today["time_of_day"])

# Create a time index (adjust as needed)
now_local.strftime("%Y-%m-%dT")
time_index = pd.date_range(start=now_local.strftime("%Y-%m-%dT")+str(START_HOUR)+':00:00', periods=len(df_yesterday), freq='5min')
# time_index = pd.date_range(start='2024-08-10T'+str(START_HOUR)+':00:00', periods=len(df_yesterday), freq='5min')

# Plot the data
fig = go.Figure()

# Yesterday's average
fig.add_trace(
    go.Scatter(
        # x=df_yesterday["time_of_day"],
        x = time_index,
        y=df_yesterday["avg"],
        mode="lines",
        name="Avg Yesterday",
        line=dict(
            # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            color="#60c6fc"
        ),
    )
)
# Yesterday's Gust
fig.add_trace(
    go.Scatter(
        # x=df_yesterday["time_of_day"],
        x = time_index,
        y=df_yesterday["gust"],
        mode="lines",
        name="Gust Yesterday",
        line=dict(
            # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            color="#fc9960"
        ),
    )
)
# Yesterday's direction
fig.add_trace(
    go.Scatter(
        # x=df_yesterday["time_of_day"],
        x = time_index,
        y=df_yesterday["deg"],
        mode="lines",
        name="Direction Yesterday",
        line=dict(
            # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            color="#97c9c1"
        ),
    )
)
# Today's average
fig.add_trace(
    go.Scatter(
        # x=df_today["time_of_day"],
        x = time_index,
        y=df_today["avg"],
        mode="lines",
        name="Avg Today",
        line=dict(
            #color=f"rgba({colors_today[0][0]*255}, {colors_today[0][1]*255}, {colors_today[0][2]*255})"
            color="#0328fc"
        ),
    )
)
# Today's Gust
fig.add_trace(
    go.Scatter(
        # x=df_today["time_of_day"],
        x = time_index,
        y=df_today["gust"],
        mode="lines",
        name="Gust Today",
        line=dict(
            # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            color="#FF0000"
        ),
    )
)
# Today's Direction
fig.add_trace(
    go.Scatter(
        # x=df_today["time_of_day"],
        x = time_index,
        y=df_today["deg"],
        mode="lines",
        name="Direction Today",
        line=dict(
            # color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
            color="#34c73b"
        ),
    )
)
# Add a horizontal reference line at y=4.5
fig.add_shape(
    type="line",
    x0=min(time_index),  # Extend the line across the entire x-axis range
    y0=4.5,
    x1=max(time_index),
    y1=4.5,
    line=dict(
        color="#967193",
        width=2,
        dash="dashdot",
    ),
)

# Add a label to the reference line
fig.add_annotation(
    x=max(time_index),
    y=4.5,
    text="NE 45",
    showarrow=False,
    font=dict(
        size=12,
        color="#967193"
    ),
    xanchor='right',
    yanchor='top'
)

# Update layout
fig.update_layout(
    title="Kanaha: Today vs Yesterday",
    xaxis_title="Time of Day",
    yaxis_title="Wind Speed in MPH",
    legend_title="Legend",
)

# Show the plot
# fig.show()

fig.write_image("/home/<USER>/windytron.com/daily_delta.png",width=2048,height=900)

fig.write_html("/home/<USER>/windytron.com/daily_delta.html")
