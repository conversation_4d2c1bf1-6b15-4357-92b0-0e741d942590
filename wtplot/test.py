import pandas as pd
import plotly.graph_objects as go
import seaborn as sns
from datetime import datetime, timedelta
import pytz


def parse_data(data):
    df = pd.DataFrame(data)
    df[["avg", "gust", "lull", "dir", "deg"]] = df["value"].str.split(",", expand=True)
    df["avg"] = df["avg"].astype(float)
    df["gust"] = df["gust"].astype(float)
    df["lull"] = df["lull"].astype(float)
    df["created_at"] = pd.to_datetime(df["created_at"])
    df["created_at"] = df["created_at"].dt.tz_convert("Pacific/Honolulu")

    return df


# Sample data
data = [
    {"id": "1", "value": "24,34,17,ENE,77", "created_at": "2024-08-11T17:40:15Z"},
    {"id": "1", "value": "22,24,7,ENE,77", "created_at": "2024-08-11T18:49:15Z"},
    {"id": "1", "value": "24,34,17,ENE,77", "created_at": "2024-08-10T17:46:15Z"},
    {"id": "2", "value": "26,36,19,ENE,78", "created_at": "2024-08-10T18:45:15Z"},
]

# Parse the data
df = parse_data(data)

# Identify today's and yesterday's data
today = datetime.now(pytz.timezone("Pacific/Honolulu")).date()
yesterday = today - timedelta(days=1)

df_today = df[df["created_at"].dt.date == today]
df_yesterday = df[df["created_at"].dt.date == yesterday]

# Create color palettes
colors_today = sns.color_palette("husl", 3)
colors_yesterday = sns.light_palette("navy", 3)

# Plot the data
fig = go.Figure()
df_yesterday["time"] = range(0,2)
# Yesterday's data
fig.add_trace(
    go.Scatter(
        x=df_yesterday["time"],
        y=df_yesterday["avg"],
        mode="lines+markers",
        name="Avg Yesterday",
        line=dict(
            color=f"rgba({colors_yesterday[0][0]*255}, {colors_yesterday[0][1]*255}, {colors_yesterday[0][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=df_yesterday["time"],
        y=df_yesterday["gust"],
        mode="lines+markers",
        name="Gust Yesterday",
        line=dict(
            color=f"rgba({colors_yesterday[1][0]*255}, {colors_yesterday[1][1]*255}, {colors_yesterday[1][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=df_yesterday["time"],
        y=df_yesterday["lull"],
        mode="lines+markers",
        name="Lull Yesterday",
        line=dict(
            color=f"rgba({colors_yesterday[2][0]*255}, {colors_yesterday[2][1]*255}, {colors_yesterday[2][2]*255})"
        ),
    )
)
# Today's data
fig.add_trace(
    go.Scatter(
        x=df_today["time"],
        y=df_today["avg"],
        mode="lines+markers",
        name="Avg Today",
        line=dict(
            color=f"rgba({colors_today[0][0]*255}, {colors_today[0][1]*255}, {colors_today[0][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=df_today["time"],
        y=df_today["gust"],
        mode="lines+markers",
        name="Gust Today",
        line=dict(
            color=f"rgba({colors_today[1][0]*255}, {colors_today[1][1]*255}, {colors_today[1][2]*255})"
        ),
    )
)
fig.add_trace(
    go.Scatter(
        x=df_today["time"],
        y=df_today["lull"],
        mode="lines+markers",
        name="Lull Today",
        line=dict(
            color=f"rgba({colors_today[2][0]*255}, {colors_today[2][1]*255}, {colors_today[2][2]*255})"
        ),
    )
)


# Update layout
fig.update_layout(
    title="Wind Data: Today vs Yesterday",
    xaxis_title="Time",
    yaxis_title="Values",
    legend_title="Legend",
    xaxis=dict(tickformat="%H:%M", title_font=dict(size=14), tickfont=dict(size=12)),
)

# Show the plot
fig.show()
