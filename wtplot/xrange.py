import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta, timezone
import pytz
import requests

# Sample data in the provided format with some missing entries
# Get the current time in the Pacific/Honolulu timezone
now_local = datetime.now(pytz.timezone("Pacific/Honolulu"))
yesterday_start_local = now_local.replace(
    hour=0, minute=0, second=0, microsecond=0
) - timedelta(days=1)
yesterday_start_utc = yesterday_start_local.astimezone(pytz.utc)
now_utc = now_local.replace(second=0, microsecond=0).astimezone(pytz.utc)

# Format the timestamps in ISO 8601 format with Zulu (Z) suffix
yesterday_start_zulu = yesterday_start_utc.isoformat().replace("+00:00", "Z")
now_utc_zulu = now_utc.isoformat().replace("+00:00", "Z")

# Print the results
# print("Start of Yesterday in Zulu Time:", yesterday_start_zulu)
# print("Current Time in UTC (Zulu Time):", now_utc_zulu)
# exit()
#  $ curl -H "X-AIO-Key: {io_key}"
# "https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z"
username = "tavdog"
key = "0f0502cadd574ea390fe273d19971a12"
feeds = ["kanaha-average", "kanaha-gust", "kanaha-lull", "kanaha-dir-deg"]
# https://io.adafruit.com/api/v2/{username}/feeds/{feed_key}/data?start_time=2019-05-04T00:00Z&end_time=2019-05-05T00:00Z


url = (
    "https://io.adafruit.com/api/v2/%s/feeds/wind-data.%s/data/chart?start_time=%s&end_time=%s"
    % (username, feeds[0], yesterday_start_zulu, now_utc_zulu)
)
print("url:", url)

response = requests.get(url)
if response.status_code == 200:
    response_dict = response.json()
else:
    print(f"Error: {response.status_code}")

# now we want to split the
# Convert the data into a DataFrame
data = response_dict["data"]
# Convert the data into a DataFrame
df = pd.DataFrame(data, columns=["Timestamp", "Value"])

# Convert the 'Timestamp' column to datetime and localize to UTC
df["Timestamp"] = pd.to_datetime(df["Timestamp"])

# Convert the timestamps to a specific local timezone (e.g., 'America/Los_Angeles')
local_tz = pytz.timezone("Pacific/Honolulu")
df["Timestamp"] = df["Timestamp"].dt.tz_convert(local_tz)

# Extract the date and time parts
df["Date"] = df["Timestamp"].dt.date
df["Time"] = df["Timestamp"].dt.time

# Convert 'Value' column to float, handling missing data
df["Value"] = pd.to_numeric(df["Value"], errors="coerce")

# Convert the 'Time' column to datetime objects for plotting, with a date placeholder
df["DateTime"] = pd.to_datetime(df["Date"].astype(str) + " " + df["Time"].astype(str))

# Pivot the data to align times for different dates
df_pivot = df.pivot(index="DateTime", columns="Date", values="Value")

# Create a plotly figure
fig = go.Figure()

# Plot each day on the same graph using lines, connecting gaps
for date in df_pivot.columns:
    fig.add_trace(
        go.Scatter(
            x=df_pivot.index,
            y=df_pivot[date],
            mode="lines",  # Use 'lines' mode for a line chart
            name=str(date),
            connectgaps=True,  # Interpolate missing data
        )
    )

# Update layout with x-axis range from 8 AM to 8 PM
fig.update_layout(
    title="Overlay of Two Days' Data with Interpolated Gaps",
    xaxis_title="Time of Day (HH:MM:SS)",
    yaxis_title="Value",
    xaxis=dict(
        title="Time of Day",
        tickformat="%H:%M:%S",
        range=[
            pd.Timestamp("2024-08-08 08:00:00"),
            pd.Timestamp("2024-08-08 20:00:00"),
        ],  # Adjusted for plotting
    ),
    yaxis=dict(title="Values"),
    legend_title="Date",
)

# Show the plot
fig.show()
