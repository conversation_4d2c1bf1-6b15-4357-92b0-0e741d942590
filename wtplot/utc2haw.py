from datetime import datetime
import pytz

# The UTC timestamp string
utc_time_str = "2024-08-11T10:45:14Z"

# Convert the string to a datetime object
utc_time = datetime.strptime(utc_time_str, "%Y-%m-%dT%H:%M:%SZ")

# Set the timezone to UTC
utc_time = utc_time.replace(tzinfo=pytz.utc)

# Convert to Pacific/Honolulu timezone
honolulu_time = utc_time.astimezone(pytz.timezone("Pacific/Honolulu"))

# Print the result
print("UTC Time:", utc_time)
print("Honolulu Time:", honolulu_time)
