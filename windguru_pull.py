import requests
import re
import os
import sys
import json
import subprocess
import pytz
from datetime import datetime

DEBUG = False

def deg_to_cardinal(degrees):
    """
    Convert wind direction from degrees to cardinal direction (e.g. N, NNW, ESE, etc.)
    
    Parameters:
    degrees (float): Wind direction in degrees
    
    Returns:
    str: Cardinal direction corresponding to the input degrees
    """
    # Define a list of tuples containing degree ranges and their corresponding cardinal directions
    directions = [
        "N", "NNE", "NE", "ENE",
        "E", "ESE", "SE", "SSE",
        "S", "SSO", "SO", "OSO",
        "O", "ONO", "NO", "NNO"
    ]
    # Adjust degrees to ensure 0 to 360 mapping.
    index = int((degrees + 11.25) // 22.5) % 16
    return directions[index]

def full_stamp(time):

    # Define the original string
    original_string = time

    # Split the string into time part
    time_str, tz_str = original_string.split()

    # Create a timezone object from the timezone name
    wet_tz = pytz.timezone(tz_str)

    # Parse the given time string into a datetime object with the current date
    current_date = datetime.now(wet_tz).strftime("%Y-%m-%dT")
    return(current_date+time_str)

if not DEBUG:
    url = 'https://www.windguru.net/int/iapi.php?q=station_list&id_type=0&seconds=1800&seconds_alive=172800&WGCACHEABLE=30'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
        'Referer': 'https://www.windguru.cz/station/418'
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        with open('windguru_all_stations.json', 'w') as file:
            file.write(response.text)
        print("Data saved to windguru_all_stations.json")
    else:
        print(f"Failed to retrieve data. Status code: {response.status_code}")

# load up the windguru_all_stations.json
with open('windguru_all_stations.json', 'r') as f:
    spot_data = json.load(f)

with open("windguru_spots.json", 'r') as json_file:
    spot_dict = json.load(json_file)

spot_list = ','.join(spot_dict.keys())
print(f"doing spots: {spot_list}")

# Create a lookup dictionary with station_id as keys
lookup_dict = {entry["id_station"]: entry for entry in spot_data}
for spot_id in spot_dict.keys():
    print(f"doing {spot_id}")
    try:
        spot_data = lookup_dict[int(spot_id)]['weather']
        j = dict()
        j["avg"] = int(spot_data['wind_avg'] + 0.5)
        j["gust"] = int(spot_data['wind_max'] + 0.5)
        j["lull"] = int(spot_data['wind_min'] + 0.5)
        j["dir_card"] = deg_to_cardinal(spot_data['wind_direction']) or 'N'
        j["dir_deg"] = int(spot_data['wind_direction'])
        j["stamp"] = full_stamp(spot_data['time_string'])
        j["label"] = spot_dict[spot_id]['name'].replace('_',' ').title()
        if "extra_fields" in spot_dict[spot_id]:
            for field_name in spot_dict[spot_id]['extra_fields']:
                j['aux1'] = str(spot_data[field_name]) + " C"
        basename = spot_dict[spot_id]['name'].lower()
        print("writing " + basename + ".json")
        with open(basename +'.json', "w") as json_file:
                json_file.write(json.dumps(j))
    except Exception as e:
        print(e)

