# providers are here
providers:
  # weatherflow:
  #   module: "ik_multi"
  #   caller: main
  #   args:
  #     - spots_dict
  #     - debug

  # weatherflow_timer:
  #   command: python3
  #   script: ik_api_timer.py
  #   args:
  #     - "{id}"
      
  noaa_ndbc:
    module: "noaa_ndbc"
    caller: main
    args:
      - spots_dict
      # - debug
  
  # flightaware_da:
  #   script: da.sh

inputs:
    mokapu_point:
      type: buoy
      id: 51202
      data_types: [swell]
      provider: noaa_ndbc
      interval_min: 30

    kaneohe_bay:
      type: buoy
      id: 51207
      data_types: [swell]
      provider: noaa_ndbc
      interval_min: 30

    kailua:
      type: anemo
      id: 429
      data_types: [wind,temperature]
      provider: weatherflow
      interval_min: 5

    ogg:
      type: anemo
      id: 643
      data_types: [wind]
      provider: weatherflow
      interval_min: 20

    # pauwela:
    #   type: buoy
    #   id: 51205
    #   data_types: [swell]
    #   provider: noaa_ndbc
    #   interval_min: 15
    # maui_da:
    #   type: density_altitude
    #   id: PHOG
    #   data_type: [da]
    #   provider: flightaware_da
    #   interval_min: 60

default_output_channels:
  txt_only: &txt_only
    output_channel:
      - "file:txt"

  mqtt_and_txt: &mqtt_and_txt
    output_channel:
      - "file:txt"
      - "mqtt:{region}/{name}"  # Use Python/Jinja templating to substitute {name}

outputs:
  - name: kailua
  - desc: kailua wind / makapu swell
    primary_data:
      type: wind
      input: kailua
    aux_data:
      - type: swell
        input: mokapu_point
      - type: swell
        input: mokapu_point
    <<: *mqtt_and_txt

    # - name: "Maui Density Altitude"
    #   date_types: [da]
    #   inputs:
    #     - maui_da
    #   output_formats:
    #     - txt

    # name
    #