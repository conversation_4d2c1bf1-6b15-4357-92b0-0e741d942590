#!/bin/sh
cd ~/wildc.net/wind/ ; mv da.txt da.old ;
TZ=":US/Hawaii" date +%H:%M >> da.txt;
FT=$(wget -U "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36" wget -U "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36" https://flightaware.com/resources/airport/PHOG/weather -O - |grep "\sft" |head -1 |awk -F '>' '{print $4}' |awk '{print $1}'|sed s/,//);
echo $FT'ft' > raw_da.txt;
cat raw_da.txt >> da.txt;
echo "----" >> da.txt;
cat da.old >> da.txt;
# now do the webhook to send to adafruit IO
curl -F value=$FT https://io.adafruit.com/api/v2/webhooks/feed/CAjFzemTvrKPiseiKKidCTwhFTPM
