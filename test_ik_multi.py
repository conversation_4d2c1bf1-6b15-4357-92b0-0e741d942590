#!/usr/bin/env python3
import ik_multi
import json

# Define a small set of spots for testing
test_spots = {
    "166192": {"name": "kanaha"},  # Kanaha Beach, Maui
    "181145": {"name": "kihei"}    # <PERSON><PERSON><PERSON>, <PERSON><PERSON>
}

# Call the main function with our test spots and no file output
print("Fetching wind data...")
result = ik_multi.main(spots_dict=test_spots)

# Print the results in a readable format
print("\nResults:")
for spot_name, data in result.items():
    print(f"\n{data['label']}:")
    print(f"  Wind: {data['dir_card']} {data['avg']}g{data['gust']} mph")
    print(f"  Direction: {data['dir_deg']}°")
    print(f"  Timestamp: {data['stamp']}")
    print(f"  Wind string: {data['wind_string']}")

# Save the results to a JSON file for reference
with open("test_results.json", "w") as f:
    json.dump(result, f, indent=2)
    print("\nResults saved to test_results.json")