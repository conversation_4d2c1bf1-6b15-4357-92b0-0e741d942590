# Multi spot ik fetch
# Fetch all the spots in one request.

import requests
import re
import os
import sys
import json

spotarray = [
  {"name":"makapuu", "id":"183525"},
  {"name":"kailua", "id": "429"},
  {"name":"molokini", "id":"170422"},
]
id_array = [spot['id'] for spot in spotarray]
spot_list = ','.join(id_array)
print(spot_list)

api_url = 'http://api.weatherflow.com/wxengine/rest/spot/getSpotDetailSetByList?units_wind=mph&spot_list='+spot_list+'&wf_token='

# returns wftoken
def wf_login():
    #print( "doing full login" )
    post_data = {
        'isun' : '<EMAIL>',
        'ispw' : 'jungle',
        'iwok.x' : 'Sign In',
        'app' : '' ,
        'rd' : '' 
    }
    post_headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192',
        'Content-Type':  'application/x-www-form-urlencoded'
    }
    url1 = 'https://secure.ikitesurf.com/'
 
    #print("posting"+url1)
    resp2 = requests.head(url1, data=post_data)
    headers = str(resp2.headers)
    #print(headers)
    token = re.search("wfToken=(.*);expires", headers ).group(1)
    
    with open("wftoken", "w") as wf_token_file:
        wf_token_file.write(token)
    
    return token

def fetch_data(token):
    response = requests.get(api_url+token)
    result = response.text
    #print (response.headers)
    #print (result)
    if re.search("Invalid weatherflow token|paid membership",result):# or data[0] is None:
        # token has gone stale, refresh
        token = wf_login()
        response = requests.get(api_url+token)
        result = response.text

    j = json.loads(result)
    print(j)
    for i in range(0,len(spotarray)):
        try:
            spotarray[i]['data_array'] = j["spots"][i]["stations"][0]["data_values"][0][0:7]
        except:
            print("bad json data structure")
            exit(1) 

    
# restore session object from filesystem
exists = os.path.isfile("wftoken")
if exists:
    in_file = open("wftoken", "r")
    wftoken = in_file.read().rstrip()
else:
    wftoken = wf_login()
    

fetch_data(wftoken)
        
for spot in spotarray: # changed the source array to have 2 extra fields at the start thus the +2 math below.
    wind_avg = int(spot['data_array'][0+2]+0.5)
    wind_gust = int(spot['data_array'][2+2]+0.5)
    wind_lull = int(spot['data_array'][1+2]+0.5)
    wind_dir = spot['data_array'][4+2].strip('"')
    wind_dir_degrees = spot['data_array'][3+2]
    local_stamp = spot['data_array'][0]
    # #ENE 56 16g20
    wind_string = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_avg) + "g" + str(wind_gust)
    #print (spot['name'] + " : " + wind_string)
    # 
    # # need to output this : ENE 56 16g20
    file_name = spot['name'] + ".txt"
    print(f"writing {file_name} : {wind_string}")
    with open(file_name, "w") as text_file:
        text_file.write(wind_string)

    try:
        # generate a simple json object with wind data and name
        j = dict()
        j["avg"] = wind_avg
        j["gust"] = wind_gust
        j["lull"] = wind_lull
        j["dir_card"] = wind_dir
        j["dir_deg"] = wind_dir_degrees
        j["stamp"] = local_stamp.replace(' ',"T")
        j["label"] = spot['name'].capitalize()

#        print("writing " + spot['name'] + ".json")
        with open(spot['name']+'.json', "w") as json_file:
                json_file.write(json.dumps(j))
    except Exception as e:
        print(e)      
