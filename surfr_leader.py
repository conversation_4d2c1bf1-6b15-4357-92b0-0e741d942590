#!/usr/bin/python3

import json
import requests
import os
import time
import sys
import argparse

#setup argument parser
parser = argparse.ArgumentParser()
parser.add_argument("-d", "--debug", help="debug mode", action='store_true')
parser.add_argument("-t", "--tidbyt", help="output to tidbyt with key", action='store_true')
parser.add_argument('-s', '--spotid', help='spotid', default='10613') # <- maui
parser.add_argument('-l', '--leaderboard', help='leaderboard', default='leaderboard_10613.json')
parser.add_argument('-f', "--fresh", help="new leaderboard file", action='store_true')
parser.add_argument('-u', "--units", help="units", default='metric')
args = parser.parse_args() 
debug = args.debug
tidbyt = True #args.tidbyt always do tidbyt, thanks.

# set the request url to the competition servlet
url = 'https://kiter-271715.appspot.com/CompetitionServlet?spotid=%s&ajax=0&video=0' % (args.spotid)

sample_string = '{"name":"TAVIS","country":"null","height":12.1,"airtime":5.8,"distance":44,"kmu":56,"landinggforce":4.9,"base64":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/"}'
last_data_string = ''
data = None
# each second, get the url and parse the json response
count = 0
status = "cleared"
# set leaderboard file
lb_json_filename = "leaderboard_%s.json" % (args.spotid)
hang_lb_json_filename = "hang_leaderboard_%s.json" % (args.spotid)
alltime_json_filename = "alltime_%s.json" % (args.spotid)
# read in leaderboard.json
leaderboard = {'-----':0,'----':0,'---':0}
hang_leaderboard = {'-----':0,'----':0,'---':0}
alltime_leaderboard = {'-----':0,'----':0,'---':0}

#if alltime.json exists, read in alltime.json
if os.path.isfile(alltime_json_filename):
    print("loading alltime leaderboard")
    with open(alltime_json_filename, 'r') as f:
        alltime_leaderboard = json.load(f)
    print(alltime_leaderboard)
# if leaderboard.json exists, read in the leaderboard
if os.path.isfile(lb_json_filename) and not args.fresh:
    print("loading leaderboard")
    with open(lb_json_filename) as f:
        leaderboard = json.load(f)
print(leaderboard)

# if hang_leaderboard.json exists, read in the leaderboard
if os.path.isfile(hang_lb_json_filename) and not args.fresh:
    print("loading hang_leaderboard")
    with open(hang_lb_json_filename) as f:
        hang_leaderboard = json.load(f)
print(hang_leaderboard)


sorted_leaderboard = []
sorted_alltime_leaderboard = []
hang_sorted_leaderboard = []


def add_to_alltime_leaderboard(data):
    new_high = False
    global alltime_leaderboard
    if debug: print(alltime_leaderboard)
    leaderdict = {}
    #print(leaderboard)
    if data['name'] not in alltime_leaderboard:
        print("new alltime rider : %s" % (data['name']))
        alltime_leaderboard[data['name']] = data['height']
        new_high = True
    elif data['height'] > alltime_leaderboard[data['name']]:
        print("new alltime high for %s : %f" % (data['name'],data['height']))
        alltime_leaderboard[data['name']] = data['height']
        new_high = True
    global sorted_alltime_leaderboard
    # sort alltime_leaderboard by height
    sorted_alltime_leaderboard = sorted(alltime_leaderboard.items(), key=lambda x: x[1], reverse=True)
    return new_high
    
#define a function add name and height to leaderboard dict
def add_to_leaderboard(data):
    global leaderboard
    leaderdict = {}
    #print(leaderboard)
    if data['name'] not in leaderboard:
        print("new rider : %s" % (data['name']))
        leaderboard[data['name']] = data['height']
    elif data['height'] > leaderboard[data['name']]:
        print("new high for %s : %f" % (data['name'],data['height']))
        leaderboard[data['name']] = data['height']
    global sorted_leaderboard
    # sort leaderboard by height
    sorted_leaderboard = sorted(leaderboard.items(), key=lambda x: x[1], reverse=True)
    for i in range(len(sorted_leaderboard[:3])):
        leaderdict[sorted_leaderboard[i][0]] = sorted_leaderboard[i][1]
    # if name in the top 3 of leaderboard
    if data['name'] in leaderdict:
        return True
    else:
        return False

#define a function add name and airtime to leaderboard dict
def add_to_hang_leaderboard(data):
    global hang_leaderboard
    leaderdict = {}
    #print(hang_leaderboard)
    if data['name'] not in hang_leaderboard:
        print("new rider : %s" % (data['name']))
        hang_leaderboard[data['name']] = data['airtime']
    elif data['airtime'] > hang_leaderboard[data['name']]:
        print("new high for %s : %f" % (data['name'],data['airtime']))
        hang_leaderboard[data['name']] = data['airtime']
    global sorted_hang_leaderboard
    # sort hang_leaderboard by airtime
    sorted_hang_leaderboard = sorted(hang_leaderboard.items(), key=lambda x: x[1], reverse=True)
    for i in range(len(sorted_hang_leaderboard[:3])):
        leaderdict[sorted_hang_leaderboard[i][0]] = sorted_hang_leaderboard[i][1]
    # if name in the top 3 of leaderboard
    if data['name'] in leaderdict:
        return True
    else:
        return False

command = 'cd ~/wildc.net/wind/pixlet ; ./tidbyt_clear.sh ; ./pixlet render surfr_alltime_leader.star data=\'' + json.dumps(alltime_leaderboard) + '\' && ./tidbyt_surfr_alltime_leader.sh'
               
if debug: print(command)
os.system(command)
print("Started")
while True:

    if count > 4 and status == "displayed":
        # push the leaderboard to tidbyt
        command = 'cd ~/wildc.net/wind/pixlet/ ; ./tidbyt_surfr_alltime_leader.sh ; ./tidbyt_surfr_leader.sh'
        if debug: print(command)
        os.system(command)
        status = "cleared"

    if debug and False:   # never happens
        data = json.loads(sample_string)
    else:
        # try block to catch errors
        try:
            response = requests.get(url)
            # parse the json response
            #if debug: print('Response: ' + response.text)
            data = json.loads(response.text)
        except:
            break

    # if we have data, and base64 key is present, delete the base64 key
    if data:
        if 'base64' in data:
            del data['base64']
        # if data is different from the last data, print the data
        
        if args.units == "feet":
            data['height'] = data['height']*3.28084

        if json.dumps(data) != last_data_string:
            # print the data
            print('.')
            print(data['name']+ " " + str(data['height']))
            last_data_string = json.dumps(data)
            

            #./pixlet render surfr.star data='{"name":"TAVIS","country":"null","height":8.7,"airtime":5.8,"distance":44,"kmu":41,"landinggforce":4.9}'
            command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr.star data=\'' + json.dumps(data) + '\' && ./tidbyt_surfr.sh'
            if debug: print(command)
            os.system(command)
            count = 0
            status = "displayed"

            # build the system for mqtt
            #command = '. ~/.bashrc ; cd ~/wildc.net/wind ; ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass \$\$titz\* -p maui/surfr_trf -r -m {}'.format(json.dumps(data))
            # print the commmand
            #if debug: print(command)
            # execute system command
            #os.system(command)

            print('+', end='')
            # add data to leaderboard dict; returns true if in top 3
            if add_to_leaderboard(data):
                # execute the pixlet render command to generate the leaderboard
                leaderdict = {}
                for i in range(len(sorted_leaderboard)):
                    leaderdict[sorted_leaderboard[i][0]] = sorted_leaderboard[i][1]
                    #print(sorted_leaderboard)
                # if we just displayed then just generate a leaderboard graphic, don't push to tidbyt yet
                if status == "displayed":
                    command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_leader.star data=\'' + json.dumps(leaderdict) + '\''
                else:
                    command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_leader.star data=\'' + json.dumps(leaderdict) + '\' && ./tidbyt_surfr_leader.sh'
                #print(command)
                os.system(command)
                # write out leaders as json to file
                with open(lb_json_filename , 'w') as f:
                    json.dump(leaderboard, f)

            if add_to_hang_leaderboard(data):
                # execute the pixlet render command to generate the hand leaderboard
                leaderdict = {}
                for i in range(len(sorted_hang_leaderboard)):
                    leaderdict[sorted_hang_leaderboard[i][0]] = sorted_hang_leaderboard[i][1]
                    #print(sorted_hang_leaderboard)
                # if we just displayed then just generate a hang_leaderboard graphic, don't push to tidbyt yet
                if status == "displayed":
                    command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_hang_leader.star data=\'' + json.dumps(leaderdict) + '\''
                else:
                    command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_hang_leader.star data=\'' + json.dumps(leaderdict) + '\' && ./tidbyt_surfr_hang_leader.sh'
                #print(command)
                os.system(command)
                # write out leaders as json to file
                with open(hang_lb_json_filename , 'w') as f:
                    json.dump(hang_leaderboard, f)

            if add_to_alltime_leaderboard(data):
                # execute the pixlet render command to generate the update altime leaderboard
                leaderdict = {}
                for i in range(len(sorted_alltime_leaderboard)):
                    leaderdict[sorted_alltime_leaderboard[i][0]] = sorted_alltime_leaderboard[i][1]
                    #print(sorted_alltime_leaderboard)
                # if we just displayed then just generate a leaderboard graphic, don't push to tidbyt yet
                if status == "displayed":
                    command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_alltime_leader.star data=\'' + json.dumps(leaderdict) + '\''
                else:
                    command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr_alltime_leader.star data=\'' + json.dumps(leaderdict) + '\' && ./tidbyt_surfr_alltime_leader.sh'
                #print(command)
                os.system(command)
                # write out leaders as json to file
                print("Updating "+ alltime_json_filename)
                with open(alltime_json_filename , 'w') as f:
                    json.dump(alltime_leaderboard, f)


        else:
            print('-',end='')

    # if debug is true, quit the program
    #if debug:
    #    break

    # wait one second
    time.sleep(1)
    print('.', end='')
    sys.stdout.flush()
    count = count + 1

    

