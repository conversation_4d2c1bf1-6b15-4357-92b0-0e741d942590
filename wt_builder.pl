use warnings;
use strict;
use IO::Socket::SSL;
use CGI qw(:standard);
use DateTime;


use strict;
use warnings;

use strict;
use warnings;

sub write_json {
    my ($filename, $label, $wind, $aux1, $aux2, $stamp) = @_;
    
    # Parse the wind string
    $wind =~ /^([A-Z]+)\s+(\d+)\s+(\d+)g(\d+)/;
    my ($dir_card, $dir_deg, $avg, $gust) = ($1, $2, $3, $4);

    # Hardcoded lull value
    my $lull = 0;

    # Generate JSON string by concatenation
    my $json = '{' .
        '"label": "' . $label . '", ' .
        '"avg": ' . $avg . ', ' .
        '"gust": ' . $gust . ', ' .
        '"dir_card": "' . $dir_card . '", ' .
        '"dir_deg": ' . $dir_deg . ', ' .
        '"stamp": "' . $stamp . '", ' .
        '"aux1": "' . $aux1 . '", ' .
        '"aux2": "' . $aux2 . '"' .
        '}';

    # Write JSON to file
    open my $file, '>', $filename or die "Could not open file '$filename': $!";
    print $file $json;
    close $file;
}

# Example usage
my $wind = 'NNW 342 6g7';
my $aux1 = '2.0f,17s,NW307';
my $aux2 = '1034L0.5~1541H1.5';
my $label = 'Kihei';
my $stamp = '2024-11-02T09:45:39';
my $filename = 'output.json';

#write_json($filename, $wind, $aux1, $aux2, $label, $stamp);

my $dt = DateTime->now(
      time_zone  => '-1000',
  );
my $LOG ;
my $test = 0;

    
$wind = `cat kanaha`;
my $wind_kts = `cat kanaha_kts`;

chomp $wind;

# get the buoy wave data
my $waves = `cat pauwela.txt`;
#my $waves = `cat sl_kanaha.txt`;
chomp $waves;
my $lanai = `cat lanai_buoy.txt`;
chomp $lanai;
my $pauwela_wind_swell = `cat pauwela_wind_swell.txt`;
chomp $pauwela_wind_swell;
my $b51001 = `cat 51001.txt`;
chomp $b51001;
my $da = `cat raw_da.txt`;
chomp $da;
my $tides = `cat kahului_tides.txt`;
chomp $tides;
my $kihei_wind = `cat kihei`;
chomp $kihei_wind;
my $kihei_tide = `cat kihei_tides.txt`;
chomp $kihei_tide;
my $hookipa_wind = `cat WS8A`;
chomp $hookipa_wind;


#my $waves = `cat noaa_buoy_51201.txt`;


my $on_the_half = 0;
# update the windlog on the half hour only
if (($dt->minute() == 0 || $dt->minute() == 30) || $test) {
	$on_the_half = 1;
	
}
my $hourmin = "T" . $dt->hour() . ":" . sprintf("%02d", $dt->minute());


my $quarter = int($dt->minute() / 15);

# and update the avg_history file with 5 minute average
if (($dt->hour >= 10 && $dt->hour <= 18) || $test) {  # 8 hours 10-6
    	my $avg_str = `cat avg_history.txt`;
    	chomp $avg_str;
    	print $avg_str . "\n";
    	my @avg_array = split //, $avg_str;
    	print "hour is " . $dt->hour();
    	my $step = int(($dt->hour()-10) * 4 + (int($dt->minute)/15));
    	$wind =~ m/\s(\d\d)?g/;
        print " step is $step , new_val is $1 \n";
    	
    	my $new_val = $1;
    	my $old_val = $avg_array[$step]*5;
    	print "old_val is $old_val \n";
    	# calculate the average
    	my $five_step = int(($dt->minute() - ($quarter * 15)) / 5);
    	print "five_step is $five_step \n";
    	my $new_avg = int((($old_val * $five_step + $new_val) / ($five_step+1))/5);
    	
        $avg_array[$step] = $new_avg;
    	print "graph new_avg is " . $new_avg . "\n"; 
        
        #print "new array is @avg_array";
        my $avgoutput = join("",@avg_array);
        print $avgoutput;
        system("echo $avgoutput > avg_history.txt");
        system("echo $avgoutput > kanaha.hist");
    
}

my $graph = `cat avg_history.txt`;
chomp $graph;
#my $wdir_deg = get_wind_degrees();
#$wind =~ s/\s/ $wdir_deg /;  # insert the degrees (replace first space with space degrees space)
print "wind is " . $wind . "\n";
open $LOG , ">wind.txt" ;	
print $LOG $wind." , ". $waves . " - ".$dt . "+" . $graph . "\n";
close $LOG;
open $LOG , ">wind_lora.txt" ;	
print $LOG $wind." , ". $waves . "_" . $lanai . " - ".$hourmin . "\n";
close $LOG;
open $LOG , ">wind_kts.txt" ;	
print $LOG $wind_kts." , ". $waves . "_" . $lanai . " - ".$dt . "+" . $graph . "\n";
close $LOG;
open $LOG , ">wind_swells.txt" ;	
print $LOG $wind." , ". $waves . "_" . $lanai . " - ".$dt . "+" . $graph . "\n";
# open $LOG , ">kanaha_pauwelaG_pauwelaW.txt" ;	
# print $LOG $wind." , ". $waves . "_" . $pauwela_wind_swell . " - ".$dt . "+" . $graph . "\n";
# close $LOG;


# print $LOG generate_json($wind, $aux1, $aux2, $label, $stamp) . "\n";


# Get the current time
my $current_time = time();

# Get the file modification time (mtime) using stat
my $mtime = (stat("WS8A"))[9];

# Check if the file's mtime is less than 5 minutes ago (300 seconds)
if ($current_time - $mtime < 300) {
    # hookipa pauwelas
	open $LOG , ">hookipa_pauwelaG_pauwelaW.txt" ;	
	print $LOG $hookipa_wind." , ". $waves . "_" . $pauwela_wind_swell . " - ".$dt . "+" . "\n";
	close $LOG;
	

	# hookipa pauwela + 51101
	open $LOG , ">hookipa_pauwelaG_51001.txt" ;	
	print $LOG $hookipa_wind." , ". $waves . "_" . $b51001 . " - ".$dt . "+" . "\n";
	close $LOG;

	# hookipa + 51101
	open $LOG , ">hookipa_51001.txt" ;	
	print $LOG $hookipa_wind." , ". $b51001 . "_. - ".$dt . "+" . "Ho'okipa" . "\n";
	close $LOG;


    open $LOG , ">hookipa" ;	
    print $LOG $hookipa_wind . "\n";
    close $LOG;

    # do this in the WS8X ingester script instead.
    # print("doing CWOP");
    # system("cd ~/wildc.net/wind ; python3 WS8A_to_CWOP.py >> CWOP.log");

} else {
    print "Stale WS8A\n";
}

write_json("kanaha_pauwelaG_pauwelaW.json", "Kanaha", $wind, $waves, $pauwela_wind_swell, $dt);
write_json("kanaha_pauwela_51001.json", "Kanaha", $wind, $waves, $b51001, $dt);


open $LOG , ">kanaha_pauwela_51001.txt" ;	
print $LOG $wind." , ". $waves . "_" . $b51001 . " - ".$dt . "+" . $graph . "\n";
close $LOG;
# open $LOG , ">kanaha_pauwela_51001.json" ;	
# #print $LOG {"avg": 2, "gust": 6, "lull": 0, "dir_card": "NW", "dir_deg": 304, "stamp": "2024-11-02T09:45:39", "label": "Kihei", "aux1": "2.0f,17s,NW307", "aux2": "1034L0.5~1541H1.5"}
# close $LOG;

open $LOG , ">kanaha_pauwela_da.txt" ;	
print $LOG $wind." , ". $waves . "_" . $da . " - ".$dt . "+" . $graph . "\n";
close $LOG;
open $LOG , ">kanaha_pauwela_tides.txt" ;	
print $LOG $wind." , ". $waves . "_" . $tides . " - ".$dt . "+" . $graph . "\n";
close $LOG;
open $LOG , ">kihei_lanai_tides.txt" ;	
print $LOG $kihei_wind." , ". $lanai . "_" . $kihei_tide . " - " . $dt . "\n";
close $LOG;
open $LOG , ">wind_kts_pauwela_tides.txt" ;	
print $LOG $wind_kts." , ". $waves . "_" . $tides . " - ".$dt . "+" . $graph . "\n";
close $LOG;
open $LOG , ">wind.stamp" ;	
print $LOG $dt;
close $LOG;
open $LOG , ">wind.json" ;	
print $LOG '{"wind":"'. $wind . '"}';
close $LOG;
open $LOG , ">kanaha_pauwela_hookipa.txt" ;	
print $LOG $wind." , ". $waves . "_" . $hookipa_wind . " - ".$dt . "+" . $graph . "\n";
close $LOG;


system("python3 smart.py");
system("python3 windguru_pull.py");
# system("python3 canarias.py");
# system("python3 hoodriver.py");
# system("python3 florida.py");

system("cd ~/wildc.net/wind ; python3 kanaha_to_kts.py");

print "doing mqtt\n";
system("cd ~/wildc.net/wind ; python3 wt_mqtt.py wt_mqtt_maui_files.json");

print("doing mesh_send\n");
system("cd ~/wildc.net/wind ; python3 wt_mesh_send.py >> mesh_send.log 2>&1");
# system("cd ~/wildc.net/wind ; python3 dep_wt_mqtt_send_mesh.py >> mesh_send.log 2>&1");

print "doing IO\n";
system("cd ~/wildc.net/wind ; python3 update_io.py kanaha.csv");
system("cd ~/wildc.net/wind ; python3 update_io.py kihei.csv");


# print("doing hookipa -> windguru");
# system("cd ~/wildc.net/wind ; python3 hookipa_station_to_windguru.py >> windguru.log");

print "done";
exit;
