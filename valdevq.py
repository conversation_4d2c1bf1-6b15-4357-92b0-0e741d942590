#!/usr/bin/python3

import json
import os

#{"wind_avg":12.42,"wind_max":18.44,"wind_min":7.37,"wind_direction":120,"temperature":24.2,"mslp":null,"rh":null,"datetime":"2022-05-14 14:34:30 CEST","unixtime":1652531670}

#function to convert degrees to cardinal direction
def deg_to_card(deg):
    if deg >= 348.75 or deg < 11.25:
        return "N"
    elif deg >= 11.25 and deg < 33.75:
        return "NNE"
    elif deg >= 33.75 and deg < 56.25:
        return "NE"
    elif deg >= 56.25 and deg < 78.75:
        return "ENE"
    elif deg >= 78.75 and deg < 101.25:
        return "E"
    elif deg >= 101.25 and deg < 123.75:
        return "ESE"
    elif deg >= 123.75 and deg < 146.25:
        return "SE"
    elif deg >= 146.25 and deg < 168.75:
        return "SSE"
    elif deg >= 168.75 and deg < 191.25:
        return "S"
    elif deg >= 191.25 and deg < 213.75:
        return "SSW"
    elif deg >= 213.75 and deg < 236.25:
        return "SW"
    elif deg >= 236.25 and deg < 258.75:
        return "WSW"
    elif deg >= 258.75 and deg < 281.25:
        return "W"
    elif deg >= 281.25 and deg < 303.75:
        return "WNW"
    elif deg >= 303.75 and deg < 326.25:
        return "NW"
    elif deg >= 326.25 and deg < 348.75:
        return "NNW"

#read in valdevaqueros.json file
with open('valdevq.json') as f:
    data = json.load(f)

if 'wind_avg' in data:
    #print(data['wind_avg'])
    #NE 42 9g13 , 0.7f,9s,NNE30 - 2022-05-14T02:38:07
    
    txt_string = "%s %s %sg%s , %sC_%s - %s" % (deg_to_card(data['wind_direction']), int(data['wind_direction']), int(round(data['wind_avg'],0)), int(round(data['wind_max'],0)),data['temperature'],data['datetime'][0:10].replace('-','.'), data['datetime'][0:19].replace(' ', 'T')) 
    # write data to txt file
    print(txt_string)
    with open('valdevq.txt', 'w') as f:
        f.write(txt_string)
    
    # execute system command
    os.system("cd ~/wildc.net/wind ; perl valdevq.pl")

