###--- <PERSON><PERSON>IN DREAMHOST BLOCK
###--- Changes made to this part of the file WILL be destroyed!
# reboot notify
MAILTO="<EMAIL>"
@reboot /usr/local/bin/setlock -n /tmp/cronlock.2704187.25670 sh -c 'echo SERVER REBOOTED'
# Stern Ip counter
MAILTO="<EMAIL>"
@daily /usr/local/bin/setlock -n /tmp/cronlock.2704187.52054 sh -c wildc.net/cast/stern_ips.sh
# New Files Lister
MAILTO="<EMAIL>"
@daily /usr/local/bin/setlock -n /tmp/cronlock.2704187.167521 sh -c 'find ~ -type f -mtime -1 -print  2>/dev/null |grep -v -f ~/skipnewfileslist'
# Buoy Fetch
MAILTO=""
*/15 * * * * /usr/local/bin/setlock -n /tmp/cronlock.2704187.227340 sh -c 'cd ~/wildc.net/wind/; timeout -k 30 10 perl pauwela.pl; timeout -k 30 10 perl lanai_buoy.pl;'
###--- You can make changes below the next line and they will be preserved!
###--- END DREAMHOST BLOCK

MAILTO=""
1,6,11,16,21,26,31,36,41,46,51,56 * * * * timeout 45s sh -c 'sleep 11; cd ~/wildc.net/wind/; python3 ik_api.py la_ventana; perl windy_tron_builder_ventana.pl;'
MAILTO=""
* * * * * timeout 1m sh -c 'cd ~/wildc.net/wind/; python3 ik_api_timer.py kanaha && perl windy_tron_builder.pl || echo no build;'  >> ~/wildc.net/wind/api_timer.log 2>&1
MAILTO=""
*/5 * * * * timeout 65s sh -c 'cd ~/wildc.net/wind/; python3 ik_api.py kihei; '
#MAILTO=""
*/5 * * * * cd ~/wildc.net/wind; timeout 30s find . -name kanaha.json -mmin +10 -exec python3 ik_api.py kanaha \; -exec perl windy_tron_builder.pl \; >> api_checker.log 
#MAILTO=""
55 * * * * sh ~/wildc.net/wind/da.sh >> ~/wildc.net/wind/da.log
#MAILTO=""
*/10 * * * * cd ~/wildc.net/wind; python3 delray.py
#MAILTO=""
*/15 * * * * cd ~/wildc.net/wind; python3 aron.py
