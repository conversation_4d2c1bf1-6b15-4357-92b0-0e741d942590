#!/usr/bin/perl
use strict;


my $url = "http://www.ndbc.noaa.gov/station_page.php?station=51213"; # lanai
#my $url = "https://www.ndbc.noaa.gov/station_page.php?station=51211"; # perl harbor entrance
#my $url = "https://www.ndbc.noaa.gov/station_page.php?station=51212"; # barbers point when lanai is down
my $rawrss = `wget --timeout=10 --cache=off -o /dev/null -O - $url`;
chomp $rawrss;
$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Height is $height\n";

$rawrss =~ /Dominant\sWave\sPeriod\s\(DPD\):\<\/td\>\<td\>\s+(\d+\s+sec)\<\/td\>/;
#.+(\d+\.\d?\s+sec)\<\/td/;
my $period = $1;
$period =~ s/\s//;
$period =~ s/sec/s/;


print "Period is $period\n";

$rawrss =~ /Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;

my $direction = $1;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;
print "Direction is $direction deg \n";

unless ($height eq '' || $period eq '' || $direction eq '') {
    
    my $LOG;
    open $LOG , ">lanai_buoy.txt" ;	
    print $LOG $height.",". $period . "," . $direction . "\n";
    close $LOG;
    # run io updater curl.
    my $csv = $height.",". $period . "," . $direction;
    my $curl_command = "curl -F 'value=$csv' -H 'X-AIO-Key: 0f0502cadd574ea390fe273d19971a12'   https://io.adafruit.com/api/v2/tavdog/feeds/buoy-data.lanai-csv/data";
    print $curl_command;
    system($curl_command);
} else {
print "no data";
	my $LOG;
    open $LOG , ">lanai_buoy.txt" ;
    print $LOG "no data\n";
    close $LOG;

}

