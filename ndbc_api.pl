#!/usr/bin/perl
use strict;
use CGI qw(param);
use LWP::Simple;

my $buoy_id = param("buoy_id");
$buoy_id =~ s/[^ [:ascii:] ]//g;

#$buoy_id = "51205" unless $buoy_id;

my $rss_url = "http://www.ndbc.noaa.gov/station_page.php?station=".$buoy_id ; 

my $rawrss;
#print $rss_url;
if ($buoy_id) {
    $rawrss = get($rss_url);
} else {
    $rawrss = `cat station.html`;
}

#print $rawrss;
chomp $rawrss;
#Station 51208 - Hanalei, Kauai __ Station BRHC3 - 8467150 - Bridgeport, CT 
my $name;
$name = $1 if $rawrss =~ /Station\s+.*\s+\-\s+(.+),/;

#print "Name is $name\n";

#<td>Wind Direction (WDIR):</td>
#	<td>NNW ( 340 deg true )</td>
my $wdir;
$wdir = $1 if $rawrss =~ /\<td\>Wind\sDirection\s+\(WDIR\):\<\/td\>\s+\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
 
$wdir =~ s/\(//;
$wdir =~ s/\s//;
#print "wdir is $wdir\n";

# <td>Wind Speed (WSPD):</td>
#         <td>  7.0 kts</td>
#<td>Wind Speed (WSPD):</td>
#	<td>    3 kts</td>
my $wspd;
$wspd = $1 if $rawrss =~ /\<td\>Wind\sSpeed\s\(WSPD\):\<\/td\>\s+\<td\D*(\d+\.?\d?+\skts)\<\/td.*+/;

$wspd =~ s/\s//;
$wspd =~ s/kts//;
#print "wspd is $wspd\n";

# <td>Wind Gust (GST):</td>
# 	<td> 11.1 kts</td>
my $wgst;
$wgst = $1 if $rawrss =~ /\<td\>Wind\sGust\s\(GST\):\<\/td\>\s+\<td\D*(\d+\.?\d?+\skts)\<\/td.*+/;

$wgst =~ s/\s//;
$wgst =~ s/kts//;
#print "wgst is $wgst\n";

# <td>Air Temperature (ATMP):</td>
# 	<td> 25.3 &deg;F</td>
my $atmp;
$atmp = $1 if $rawrss =~ /\<td\>Air\sTemperature\s\(ATMP\):\<\/td\>\s+\<td\D*(\d+\.\d?+\s&deg\;F)\<\/td.*+/;

$atmp =~ s/\s//;
$atmp =~ s/&deg\;F//;
#print "atmp is $atmp\n";

# <td>Water Temperature (WTMP):</td>
# 	<td> 38.3 &deg;F</td>
my $wtmp;
$wtmp = $1 if $rawrss =~ /\<td\>Water\sTemperature\s\(WTMP\):\<\/td\>\s+\<td\D*(\d+\.\d+\s&deg\;F)\<\/td.*+/;

$wtmp =~ s/\s//;
$wtmp =~ s/&deg\;F//;
#print "wtmp is $wtmp\n";


# <td>Atmospheric Pressure (PRES):</td>
# 	<td>30.32 in</td>
my $pres;
$pres = $1 if $rawrss =~ /Atmospheric\sPressure\s\(PRES\):\<\/td\>\s+\<td\D*(\d+\.\d+\sin)\<\/td.*/;

$pres =~ s/\s//;
$pres =~ s/in//;
#print "pres is $pres\n";


#$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height;
$height = $1 if $rawrss =~ /Wave\sHeight\s\(WVHT\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;

$height =~ s/\s//;
$height =~ s/ft//;
#print "Height is $height\n";

my $period;
$period = $1 if $rawrss =~ /\<td\>Dominant\sWave\sPeriod\s\(DPD\):\<\/td\>\s+\<td\>\s+(\d+\s+sec)\<\/td\>/;
$period =~ s/\s//;
$period =~ s/sec//;
#print "Period is $period\n";

my $direction;
$direction = $1 if $rawrss =~ /\<td\>Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\s+\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
$direction =~ s/\(//;
$direction =~ s/\s//;
#$direction =~ s/\s//;

# print "Height is $height\n";
# print "Period is $period\n";
# print "Direction is $direction deg \n";

if ($name eq '') {$name = $buoy_id;}


my $json = '{ "name": "'.$buoy_id.'","error" : "no data" }';
unless ($height.$period.$direction.$wdir.$wspd.$wgst.$atmp.$wtmp.$pres eq '') {
    $json = '{
"name"   : "' . $name . '"'."\n";
   $json .= ',"WTMP" : "' . $wtmp . '"'."\n" if $wtmp ne '';
    $json .= ',"WVHT" : "' . $height . '"'."\n" if $height ne '';
    $json .= ',"DPD" : "' . $period . '"'."\n" if $period ne '';
    $json .= ',"MWD" : "' . $direction . '"'."\n" if $direction ne '';
    $json .= ',"WDIR" : "' . $wdir . '"'."\n" if $wdir ne '';
    $json .= ',"WSPD" : "' . $wspd . '"'."\n" if $wspd ne '';
    $json .= ',"GST" : "' . $wgst . '"'."\n" if $wgst ne '';
    $json .= ',"ATMP" : "' . $atmp . '"'."\n" if $atmp ne '';
    $json .= ',"WTMP" : "' . $wtmp . '"'."\n" if $wtmp ne '';
    $json .= ',"PRES": "' . $pres . '"'."\n" if $pres ne '';
    $json .= '}';
}
print "Content-type: text/json\n\n"; 
print $json;
