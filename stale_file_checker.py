#!env python3
import os
import datetime
import requests

EMAIL_ADDRESS = "<EMAIL>"
NTFY_TOPIC = "windytron"

#       FILENAME : MAX AGE IN MINUTES
fdict = {
        "kanaha_lag" : "15", # minutes
        "kanaha" : "20",
        "kihei.json" : "20",
        "sl_kanaha.txt" : "1440",
        "sl_hookipa.txt" : "1440",
        "sl_lahaina.txt" : "1440",
        "sl_olowalu.txt" : "1440",
        "sl_launiupoko.txt" : "1440",
        "kahului_tides_full.txt" : "1440",
        #"kihei_tides_full.txt" : "1640",
        "pauwela.txt" : "1440",
        "lanai_buoy.txt"  : "1440",
        "WS8A" : "30",
        # "wind_ventana.txt": "30",
        # "ventana_2.txt": "30",

}

stale_string = ""
for filename,interval in fdict.items():
        #print(filename,interval)
        # get the mtime of filename
        try:
            mtime = os.path.getmtime(filename)
            # get the current time
            now = datetime.datetime.now().timestamp()
            # if the file is older than the interval, then update
            if (now - mtime) > (int(interval) * 60):
                    #print("stale file %s" % filename)
                    stale_string += "stale file %s\n" % filename
        except Exception as e:
               print("error reading file %s %s" % filename,e)


if len(stale_string):
        print(stale_string)
        requests.post("https://ntfy.sh/"+NTFY_TOPIC , 
                data=stale_string.encode(encoding='utf-8'),
                headers={"X-Email":EMAIL_ADDRESS})


