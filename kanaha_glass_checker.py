#!env python3

import requests
from pathlib import Path
EMAIL_ADDRESS = "<EMAIL>"
# EMAIL_ADDRESS = "<EMAIL>"
NTFY_TOPIC = "kanaha_glass"
username = "tavdog"
key = "0f0502cadd574ea390fe273d19971a12"
feed = "mesh.ws8a-windavg"
# assume running at 6am hst. check tha last 30 minutes of hookipa avg
# if all values are below 10 then fire
# /api/v2/{username}/feeds/{feed_key}/data/chart
url = (
        "https://io.adafruit.com/api/v2/%s/feeds/%s/data/chart?hours=1&limit=6"
        % (username, feed)
    )
print("url:", url)

response = requests.get(url)
# print(response.json())
if response.status_code == 200:
    # print(response.json()['data'])
    data = response.json()['data']
else:
    print(f"Error: {response.status_code}")
    print(response.json())
    exit()

# Extract the second value of each item and convert to float
values = [float(item[1]) for item in data]
print(str(values))
# Calculate the average
average = sum(values) / len(values)

# convert to mph here
average = average * 2.237

print(f"average is {average} mph")

pauwela_file = Path("/home/<USER>/wildc.net/wind/pauwela.txt")
if pauwela_file.exists():
    first_value_str = pauwela_file.read_text().strip().split(",")[0]
    try:
        pauwela_first_val = int(float(first_value_str.replace("f", "")))
        print(f"First value from pauwela.txt (as int): {pauwela_first_val}")
    except ValueError:
        print("Failed to parse first value from pauwela.txt")
else:
    print("pauwela.txt not found")

if (average < 15 or min(values) < 10) and pauwela_first_val >= 3 :
    message = "might be glassy with swell at Kanaha this morning"
    requests.post("https://ntfy.sh/"+NTFY_TOPIC , 
            data=message.encode(encoding='utf-8'),
            headers={"X-Email":EMAIL_ADDRESS})
else:
    print("not glassy or flat")


