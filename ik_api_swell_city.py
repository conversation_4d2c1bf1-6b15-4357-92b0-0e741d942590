# get the wind via API call without storing the entire session object with dill

import requests
import re
import os
import sys
import json
import datetime

#spot_name = sys.argv[1]
spot_name = "swell_city"
spotdict = {
  "kanaha": "166192",
  "kahului": "4349",
  "kihei": "181145",
  "la_ventana": "183476",
  "swell_city": "440"
}
try:
    spot_id = spotdict[spot_name]
except:
    print("bad spot name")
    exit(1)
api_url = 'http://api.weatherflow.com/wxengine/rest/spot/getSpotDetailSetByList?units_wind=mph&spot_list='+spot_id+'&wf_token='

# returns wftoken
def wf_login():
    print( "doing full login" )
    post_data = {
        'isun' : 'aleciaelsasser1',
        'ispw' : 'Twins242@',
        'iwok.x' : 'Sign In',
        'app' : '' ,
        'rd' : '' 
    }
    post_headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192',
        'Content-Type':  'application/x-www-form-urlencoded'
    }
    url1 = 'https://secure.ikitesurf.com/'
 
    print("posting"+url1)
    resp2 = requests.head(url1, data=post_data)
    headers = str(resp2.headers)
    print(headers)
    token = re.search("wfToken=(.*);expires", headers ).group(1)
    
    with open("wftoken.alecia", "w") as wf_token_file:
        wf_token_file.write(token)
    
    return token

def fetch_data(token):
    response = requests.get(api_url+token)
    result = response.text
#    print (response.headers)
#    print (result)
    j = json.loads(result)
#    data = j["spots"][0]["stations"][0]["data_values"][0][2:7]
    if re.search("Invalid weatherflow token|paid membership",result) or j["spots"][0]["stations"][0]["data_values"][0][2:7][0] is None:
        # token has gone stale, refresh
        token = wf_login()
        response = requests.get(api_url+token)
        result = response.text
        
    j = json.loads(result)
    #print (j)
    try:
        data = j["spots"][0]["stations"][0]["data_values"][0][2:7]
    except:
        print("bad json data structure")
        exit(1)
        
    # write out json data to file for retrieval by gto2 devices
    # with open(spot_name+'.json', "w") as json_file:
    #     json_file.write(result)      
    # return data
    trimmed_json = dict()
    trimmed_json["spots"] = j["spots"]  
    #print("trimmed json:" + json.dumps(trimmed_json))
    # write out json data to file for retrieval by gto2 devices
    print("writing to:"+ spot_name+".json", end='')
    with open(spot_name+'.json', "w") as json_file:
        json_file.write(json.dumps(trimmed_json))      
    return data

    

# restore session object from filesystem
exists = os.path.isfile("wftoken.alecia")
if exists:
    in_file = open("wftoken.alecia", "r")
    wftoken = in_file.read().rstrip()
else:
    wftoken = wf_login()
    

data = fetch_data(wftoken)


wind_dir = data[4].strip('"')
wind_dir_degrees = data[3]            

        
# assign the wind data
#print(data)
#[2.1, 0.9, 3.6, 270,"W"]
# wind_string = data_array[1].strip('"')
wind_avg = int(data[0]+0.5)
wind_gust = int(data[2]+0.5)


# #ENE 56 16g20
wind_string = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_avg) + "g" + str(wind_gust)
print (wind_string)

#{"avg": 16, "gust": 23, "lull": 8, "dir_card": "ENE", "dir_deg": 70, "stamp": "2023-06-11T01:52:06.0"}
d = dict()
d['avg'] = str(wind_avg)
d['gust'] = str(wind_gust)
d["dir_card"] = str(wind_dir)
d["dir_deg"] = str(wind_dir_degrees)

now = datetime.datetime.now()
# Convert the date and time to a string
d["stamp"] = now.strftime('%Y-%m-%dT%H:%M:%S')
#print(d)
msg = json.dumps(d)
#print(msg)
# 
# # need to output this : ENE 56 16g20 , 2.0f,11s,NE47 - 2018-06-20T19:20:02
with open(spot_name, "w") as text_file:
    text_file.write(wind_string)

with open(spot_name+"_simple.json", "w") as text_file:
    text_file.write(msg)


import paho.mqtt.client as mqtt

mqttClient = mqtt.Client("windytron_mqtt")
mqttClient.username_pw_set("windytron_publish", '$$titz*')
mqttClient.connect('gt.wildc.net', 1884)
mqttClient.loop_start()

topic = "columbia/swell_city"
if msg != '{}':
    info = mqttClient.publish(
        topic=topic,
        payload=msg.encode('utf-8'),
        qos=0,
        retain=True,
    )
    info.wait_for_publish()
    if info.is_published():
        print("Published topic: " + topic + " : " + msg)
else:
    print("blank message")
        
mqttClient.loop_stop()    #Stop loop 
mqttClient.disconnect()
