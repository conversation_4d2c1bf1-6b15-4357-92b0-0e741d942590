#!/usr/bin/env python3

import cgi
import datetime

print("Content-Type: text/html\n")

# Get the current timestamp
timestamp = int(datetime.datetime.now().timestamp())

# Generate the HTML with the timestamp in the image URL
html_content = f"""<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Tide Graph</title>
</head>

<body style="margin: 0; height: 100%; width: 100%;">
    <table style="width: 100%; height: 100%; border: none; text-align: center;">
        <tr><td style="vertical-align: middle;"><h1>Kahului Tides</h1></td></tr>
        <tr>
            <td style="vertical-align: middle;">
                <img id="wctide_img" src="https://wildc.net/wind/kahului_tide_graph.gif?{timestamp}" alt="Tide Graph"
                    style="max-width: 100%; max-height: 100%;">
            </td>
        </tr>
    </table>
</body>

</html>"""

# Output the HTML content
print(html_content)
