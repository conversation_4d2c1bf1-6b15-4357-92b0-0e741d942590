import json
import os
import time as tm

# generate dual display output for hoodriver spots

pairs = [
    ["stevenson_viento.json", "stevenson_light.json", "viento.json"],
    ["swellcity_eventsite.json", "swell_city.json", "event_site.json"],
    ["eventsite_swellcity.json", "event_site.json", "swell_city.json"],
    ["maryhill_thewall.json", "maryhill.json", "the_wall.json"],
    ["arlington_loroc.json", "arlington.json", "loroc.json"]
]

for file, first_file, second_file in pairs:
    # your code here
    with open(first_file, 'r') as f:
        first = json.load(f)
    with open(second_file) as f:
        second = json.load(f)

    time = first['stamp'][11:16]
    list = [
        "DUALWIND",
        time,
        first['label'],
        first['dir_card'],
        first['dir_deg'],
        first['avg'],
        first['gust'],
        "",
        "",
        second['label'],
        second['dir_card'],
        second['dir_deg'],
        second['avg'],
        second['gust'],
        "",
        ""
    ]

    es_list = [str(item) for item in list]
    dualwind_es = "#".join(es_list)
    # "PACK": "DUALWIND#12:49#Kanaha + Pauwela#N#0#7#10#Gnd: 4.3f,11s,N#Wnd: 0939L0.6~1411H1.3#Kihei#NW#312#2#7#Lanai Buoy: 1.3f,13s,W262#Tides: 1058L0.3~1603H1.2"
    js = dict()
    js['PACK'] = dualwind_es
    print(js)
    with open(file, 'w') as f:
        json.dump(js, f, separators=(',', ':'))

    pak_filename = os.path.splitext(file)[0] + ".pak"
    with open(pak_filename, 'w') as f:
        f.write(js['PACK'])

