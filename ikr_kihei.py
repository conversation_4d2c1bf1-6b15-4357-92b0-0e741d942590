import requests
import re
import os
import sys
import dill as pickle

# restore session object from filesystem
exists = os.path.isfile("iksession")
if exists:
    # Store configuration file values
    in_file = open("iksession", "rb")
    pickled_session = in_file.read()
    # #
    # # use the session saved from before
    session = pickle.loads(pickled_session)
    in_file.close()
else:
    session = requests.session()
#  wind : http://wx.ikitesurf.com/spot/181145  # new kihei

resp2 = session.get('http://wx.ikitesurf.com/spot/181145') # kihei
result = resp2.text.encode('utf-8');

data_values = ""
#print result

# we have to find the line in which data_values string is located
lines = result.decode().split('\n')
for line in lines:
    if 'data_values' in line: 
        result = line

# now we get only the text after the data_values string        
data_values = re.search("data_values(.*)$", result)
#print "data_value=" + str(data_values)

# split on commas for easy array access
data_array = data_values.group(0).split(',')
print ( data_array )

# test if we have wind data and if not we do a full login and save the cookie to filesystem
if data_array[2] == 'null':
    print ("doing full login")
    session = requests.Session()

    post_data = {
        'isun' : '<EMAIL>',
        'ispw' : 'jungle',
        #'isun'  : '<EMAIL>',
        #'ispw'  : 'jun1windy',
        'iwok.x' : 'Sign In',
        'app' : 'wx' ,
        'rd' : 'spot/166192' 
    }
    post_headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192',
        'Content-Type':  'application/x-www-form-urlencoded'
    }
    url1 = 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192'

    # you need to do this twice, i think once to populate the cookies, the second to authenticate with them
    resp2 = session.post(url1, data=post_data, headers=post_headers)
    resp2 = session.post(url1, data=post_data, headers=post_headers)
    
    print (session)
    
    pickled = pickle.dumps(session)
    with open("iksession", "wb") as session_file:
        session_file.write(pickled)
    
    result = resp2.text.encode('utf-8');

    # we have to find the line in which data_values string is located
    lines = result.split('\n')
    for line in lines:
        if 'data_values' in line: 
            result = line
    
    # now we get only the text after the data_values string        
    data_values = re.search("data_values(.*)$", result)
    print ( "data_value=" + str(data_values) )
    
    # split on commas for easy array access
    data_array = data_values.group(0).split(',')
    print ( data_array )
    
    if data_array[2] == 'null':
        # it still didn't work just die.
        print ("full login was not successful")
        quit()


wind_string = data_array[1].strip('"')
wind_avg = int(float(data_array[2]))
#print wind_avg
wind_lull = int(float(data_array[3]))
#print wind_lull
wind_gust = int(float(data_array[4]))
#print wind_gust
wind_dir = data_array[6].strip('"')
#print wind_dir
wind_dir_degrees = data_array[5]
#print wind_dir_degrees
#ENE 56 16g20
wind_string = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_avg) + "g" + str(wind_gust)

# need to output this : ENE 56 16g20 , 2.0f,11s,NE47 - 2018-06-20T19:20:02
with open("kihei_wind.txt", "w") as text_file:
    text_file.write(wind_string)
    
    
# keep a running history of the last 32 datapoints for wind_avg
# read in the file which just looks like 123422242424242424240000

# in_file = open("avg_history.txt", "r")
# result = in_file.read()
# in_file.close()
# 
# result.strip()
# # [1:] should chop off the first digit
# updated = result[1:] + str(wind_avg)
# with open("avg_history.txt", "w") as text_file:
#     text_file.write(updated)
#     
    


    
    
    
    


