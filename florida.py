import json
import os
import time as tm

# generate dual display output for florida spots

with open("alligator_reef.json", 'r') as f:
    alligator_reef = json.load(f)
with open("craysfort_reef.json") as f:
    craysfort_reef = json.load(f)

# dualwind

dualwind = dict()
dualwind['type'] = "dualwind"
dualwind['stamp'] = alligator_reef['stamp']
dualwind['label'] = alligator_reef['label']
dualwind['label2'] = craysfort_reef['label']
dualwind['wind'] = f"{alligator_reef['dir_card']} {alligator_reef['dir_deg']} {alligator_reef['avg']}g{alligator_reef['gust']}"
dualwind['wind2'] = f"{craysfort_reef['dir_card']} {craysfort_reef['dir_deg']} {craysfort_reef['avg']}g{craysfort_reef['gust']}"
# dualwind['aux1'] = ""
# dualwind['aux2'] = ""
# dualwind['aux1_2'] = ""
# dualwind['aux2_2'] = ""

print(dualwind)

with open('alligator_craysfort.json', 'w') as f:
    json.dump(dualwind, f, separators=(',', ':'))

