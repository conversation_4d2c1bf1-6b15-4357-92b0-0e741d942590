import json
import os
import time as tm

# first do kanaha pauwela tides

with open("kanaha_simple.json", 'r') as f:
    north = json.load(f)
with open("pauwela.txt") as f:
    pauwela = f.read().rstrip()
with open("pauwela_wind_swell.txt") as f:
    pauwelaW = f.read().rstrip()
with open("51001.txt") as f:
    _51001 = f.read().rstrip()

with open("kahului_tides.txt") as f:
    kahului_tides = f.read()

# get hookipa
with open("WS8A") as f:
    hookipa = f.read()

# add aux1 and aux2 north items
north["aux1"] = pauwela
north["aux2"] = kahului_tides
north["label"] = "Kanaha"
with open('kanaha_nswell_ntides.json', 'w') as f:
    json.dump(north, f)


# now to kihei
with open("kihei.json", 'r') as f:
    south = json.load(f)
with open("lanai_buoy.txt") as f:
    lanai = f.read().rstrip()
with open("kihei_tides.txt") as f:
    kihei_tides = f.read()
with open("meco_ukumehame.json") as f:
    uku = json.load(f)

#
south["aux1"] = lanai
south["aux2"] = kihei_tides
south["label"] = "Kihei"
# print(south)
with open('kihei_sswell_stides.json', 'w') as f:
    json.dump(south, f)
# print(south)
print 
try:
    south_swell_height = float(south["aux1"][0:south["aux1"].index('f')])
except:
    south_swell_height = 0.0
try:
    north_swell_height = float(north["aux1"][0:north["aux1"].index('f')])
except:
    north_swell_height = 0.0


# now for logic
# if light all over then show largest swell with that side tide and wind
# if (north["avg"] > 15 and ):

# trades
if ( north["dir_card"] in ["NW","NNW","N","NNE","NE","ENE","E"] ):
    smart = north
    smart["label"] = "Kanaha"
    if (north_swell_height < south_swell_height): # show south swell
        smart["aux1"] = south["aux1"]
        smart["aux2"] = south["aux2"]
        

# non trades: kona or other
else:
    smart = south
    smart["label"] = south["label"]
    if (north_swell_height > south_swell_height): # show north swell
        smart["aux1"] = north["aux1"]
        smart["aux2"] = north["aux2"]

with open('smart.json', 'w') as f:
    json.dump(smart, f)

# now output dualwind.json

smart = north
smart['avg2'] = south['avg']
smart['gust2'] = south['gust']
smart['lull2'] = south['lull']
smart['dir_card2'] = south['dir_card']
smart['dir_deg2'] = south['dir_deg']
smart['label2'] = south['label']
smart['aux1_2'] = south['aux1']
smart['aux2_2'] = south['aux2']
smart['type'] = "dualwind"

dualwind = dict()
dualwind['type'] = "dualwind"
dualwind['stamp'] = smart['stamp']
dualwind['label'] = north['label'] + " + Pauwela"
dualwind['label2'] = south['label']
dualwind['wind'] = f"{north['dir_card']} {north['dir_deg']} {north['avg']}g{north['gust']}"
dualwind['wind2'] = f"{south['dir_card']} {south['dir_deg']} {south['avg']}g{south['gust']}"
dualwind['aux1'] = f"{pauwela}"
dualwind['aux2'] = f"{pauwelaW}"
dualwind['aux1_2'] = f"{south['aux1']}"
dualwind['aux2_2'] = ""

# print(dualwind)

with open('dualwind.json', 'w') as f:
    json.dump(dualwind, f, separators=(',', ':'))

# now do dual kanaha and hookipa
# just update the second
dualwind['label2'] = "Ho'okipa"
dualwind['wind2'] = hookipa
dualwind['aux1_2'] = f"51001: {_51001}"
dualwind['aux2_2'] = ""


with open('dualwind_kh.json', 'w') as f:
    json.dump(dualwind, f, separators=(',', ':'))

# now do dual_kh_pack.json
# DUALWIND # STYLE
# DUALWIND#16:10#Kanaha#NE#51#20#25#8f,9s,NNE##Kihei#E#91#30#35##
time = dualwind['stamp'].split('T')[1][0:5]
# dualwind_kk_mesh = f"DUALWIND#{time}#{dualwind['label']}#{north['dir_card']}#{north['dir_deg']}#{north['avg']}#{north['gust']}#{north['aux1']}#{north['aux2']}#Kihei#{south['dir_card']}#{south['dir_deg']}#{south['avg']}#{south['gust']}#{south['aux1']}#{south['aux2']}"
# dualwind_kk_mesh = f"DUALWIND#{time}#{dualwind['label']}#{north['dir_card']}#{north['dir_deg']}#{north['avg']}#{north['gust']}#{north['aux1']}#{north['aux2']}#Kihei#{south['dir_card']}#{south['dir_deg']}#{south['avg']}#{south['gust']}#{south['aux1']}#{south['aux2']}"
kk_list = [
    "DUALWIND",
    time,
    dualwind['label'],
    north['dir_card'],
    north['dir_deg'],
    north['avg'],
    north['gust'],
    "Gnd: " + north['aux1'],
    "Wnd: " + north['aux2'],
    "Kihei",
    south['dir_card'],
    south['dir_deg'],
    south['avg'],
    south['gust'],
    "Lanai Buoy: " + south['aux1'],
    "Tides: " + south['aux2']
]
kk_list = [str(item) for item in kk_list]
dualwind_kk_mesh = "#".join(kk_list)
# print(dualwind_kk_mesh)
with open('dualwind_mesh_kk.txt', 'w') as f:
    f.write(dualwind_kk_mesh)

# create dualwind_pack
dw_pack = dict()
dw_pack['PACK'] = dualwind_kk_mesh   
with open('dw_pack.json', 'w') as f:
    json.dump(dw_pack, f, separators=(',', ':'))


(card,deg,wind) = hookipa.split(' ')
(avg,gust) = wind.split('g')

kh_list = [
    "DUALWIND",
    time,
    dualwind['label'],
    north['dir_card'],
    north['dir_deg'],
    north['avg'],
    north['gust'],
    "Gnd: " + pauwela,
    "Wnd: " + pauwelaW,
    "Ho'okipa",
    card,
    deg,
    avg,
    gust,
    "51001: " + _51001,
    "Tides: " + north['aux2']
]
kh_list = [str(item) for item in kh_list]
dualwind_kh_mesh = "#".join(kh_list)
# print(dualwind_kh_mesh)
with open('dualwind_mesh_kh.txt', 'w') as f:
    f.write(dualwind_kh_mesh)

# create dualwind_pack
dw_pack['PACK'] = dualwind_kh_mesh   
with open('dw_kh_pack.json', 'w') as f:
    json.dump(dw_pack, f, separators=(',', ':'))

# now do hook51 in json
# {"avg": 17, "gust": 20, "lull": 12, "dir_card": "NE", "dir_deg": 52, "stamp": "2024-10-03T19:19:13", "label": "Kanaha", "aux1": "1.6f,11s,N", "aux2": "0837L0.6~1420H2.1"}

file_mtime = os.path.getmtime("WS8A")
current_time = tm.time()  # Get the current time in seconds since the epoch
# Check if the file has been updated within the last 5 minutes (300 seconds)
if current_time - file_mtime >= 300:
    print("WS8A is stale")
else:
    # {"avg": 17, "gust": 20, "lull": 12, "dir_card": "NE", "dir_deg": 52, "stamp": "2024-10-03T19:19:13", "label": "Kanaha", "aux1": "1.6f,11s,N", "aux2": "0837L0.6~1420H2.1"}
    hook = dict()
    card,deg,vel = hookipa.split()
    avg,gust = vel.split('g')
    hook['label'] = "Ho'okipa"
    hook['dir_card'] = card
    hook['dir_deg'] = deg
    hook['avg'] = avg
    hook['gust'] = gust
    hook['stamp'] = smart['stamp']
    hook['aux1'] = pauwela
    hook['aux2'] = pauwelaW
    with open('hookipa_pauwelaG_pauwelaW.json', 'w') as f:
        json.dump(hook, f)


    # now do hookika_pg_pw in hashpak
    pak = f"WIND#{time}#Ho'okipa + Pauwela#{card}#{deg}#{avg}#{gust}#{pauwela}#{pauwelaW}"
    print(pak)
    with open('hookipa_pauwelaG_pauwelaW.pak', 'w') as f:
        f.write(pak)


# now do Southside (kihei / ukumehame)
dualwind = dict()
dualwind['type'] = "dualwind"
dualwind['stamp'] = uku['stamp']
dualwind['label'] = "Ukumehame"
dualwind['label2'] = south['label']
dualwind['wind'] = f"{uku['dir_card']} {uku['dir_deg']} {uku['avg']}g{uku['gust']}"
dualwind['wind2'] = f"{south['dir_card']} {south['dir_deg']} {south['avg']}g{south['gust']}"
dualwind['aux2'] = ""
dualwind['aux2_2'] = ""
dualwind['aux1'] = f"{south['aux1']}"
dualwind['aux1_2'] = kihei_tides

# print(dualwind)

with open('uku_kihei.json', 'w') as f:
    json.dump(dualwind, f, separators=(',', ':'))

uk_list = [
    "DUALWIND",
    time,
    "Ukumehame",
    uku['dir_card'],
    uku['dir_deg'],
    uku['avg'],
    uku['gust'],
    dualwind['aux1'],
    "",
    "Kihei",
    south['dir_card'],
    south['dir_deg'],
    south['avg'],
    south['gust'],
    "Tides: " + south['aux2'],
    ""
]
uk_list = [str(item) for item in uk_list]
dualwind_uk_mesh = "#".join(uk_list)

with open('uku_kihei_pak.pak', 'w') as f:
    f.write(dualwind_uk_mesh)


# dualwind['type'] = "dualwind"
# dualwind['stamp'] = smart['stamp']
# dualwind['label'] = north['label'] + " + Pauwela"
# dualwind['label2'] = south['label']
# dualwind['wind'] = f"{north['dir_card']} {north['dir_deg']} {north['avg']}g{north['gust']}"
# dualwind['wind2'] = f"{south['dir_card']} {south['dir_deg']} {south['avg']}g{south['gust']}"
dualwind['aux1'] = f"{pauwela}"
dualwind['aux2'] = f"{pauwelaW}"
dualwind['aux1_2'] = f"{south['aux1']}"
dualwind['aux2_2'] = f"{uku['dir_card']} {uku['dir_deg']} {uku['avg']}g{uku['gust']}"


with open("dual_uku.json", "w") as f:
    json.dump(dualwind, f, separators=(",", ":"))


