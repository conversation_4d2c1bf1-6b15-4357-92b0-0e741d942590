<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script>
        $.ajaxSetup({
            // Disable caching of AJAX responses
            cache: false
        });

        $(document).ready(function () {
            var currentDate = new Date().toISOString();

            // Construct the URL with the currentDate variable
            var wctide = `https://wildc.net/wind/kahului_tide_graph.gif?today=${currentDate}`;
            document.getElementById('wctide_img').src = wctide;

            const url = "https://ww.windalert.com/cgi-bin/tideGraph.gif";
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth() + 1;
            const day = today.getDate();
            const tomorrow = new Date();
            tomorrow.setDate(today.getDate() + 1);
            const tyear = tomorrow.getFullYear();
            const tmonth = tomorrow.getMonth() + 1;
            const tday = tomorrow.getDate();

            const tideSUrl = `${url}&beginTime=${year}-${month}-${day}%2000:00:00&endTime=${tyear}-${tmonth}-${tday}%2023:59:59&width=830&height=290&tideSiteName=Lahaina, Maui Island, Hawaii&cb=1685592015765`;
            const tideNUrl = `${url}&beginTime=${year}-${month}-${day}%2000:00:00&endTime=${tyear}-${tmonth}-${tday}%2023:59:59&width=830&height=290&tideSiteName=Kahului,+Kahului+Harbor,+Hawaii`;

            console.log(tideNUrl);
            const imgN = document.getElementById("tideGraph_N");
            const imgS = document.getElementById("tideGraph_S");
            // imgN.src = tideNUrl;
            // imgS.src = tideSUrl;

            function timingLoad() {
                $("#kanaha").load("kanaha_lag", function (responseTxt, statusTxt, xhr) {
                    window.document.title = $("#kanaha").text();
                });
                
                $("#skillvillage").load("skillvillage", function (responseTxt, statusTxt, xhr) { });
                $("#kihei").load("kihei", function (responseTxt, statusTxt, xhr) { });
                $("#stamp").load("wind.stamp", function (responseTxt, statusTxt, xhr) { });
                $("#kanaha_waves").load("sl_kanaha.txt", function (responseTxt, statusTxt, xhr) { });
                $("#hookipa_waves").load("sl_hookipa.txt", function (responseTxt, statusTxt, xhr) { });
                $("#lahaina").load("sl_lahaina.txt", function (responseTxt, statusTxt, xhr) { });
                $("#olowalu").load("sl_olowalu.txt", function (responseTxt, statusTxt, xhr) { });
                $("#launiupoko").load("sl_launiupoko.txt", function (responseTxt, statusTxt, xhr) { });
                $("#ntide").load("kahului_tides_full.txt", function (responseTxt, statusTxt, xhr) { });
                $("#north_swell").load("pauwela.txt", function (responseTxt, statusTxt, xhr) { });
                $("#north_wind_swell").load("pauwela_wind_swell.txt", function (responseTxt, statusTxt, xhr) { });
                $("#north_swell_forecast").load("51001.txt", function (responseTxt, statusTxt, xhr) { });
                $("#south_swell").load("lanai_buoy.txt", function (responseTxt, statusTxt, xhr) { });
                $("#maui_surf").load("maui_surf.html", function (responseTxt, statusTxt, xhr) { });
            }

            setInterval(timingLoad, 30000);
            timingLoad();
        });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
        }

        header {
            background-color: #ffffff;
            color: white;
            padding: 10px 0;
            text-align: center;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            background-color: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 24px;
            text-align: center;
            margin-bottom: 10px;
            background-color: #e6e6e6;
            padding: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table,
        th,
        td {
            border: 1px solid #ddd;
        }

        th,
        td {
            padding: 10px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        .float-right {
            float: right;
            margin-left: 20px;
        }

        .link {
            color: #0044cc;
            text-decoration: none;
        }

        .link:hover {
            text-decoration: underline;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background-color: #00cc5c;
            color: white;
        }

        @media only screen and (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .float-right {
                float: none;
                margin-left: 0;
                margin-bottom: 10px;
                text-align: center;
            }

            table {
                margin-bottom: 10px;
            }

            .section-title {
                font-size: 20px;
            }
        }
    </style>
    <!-- <link rel="stylesheet" href="style.css"> -->
</head>

<body>
    <!-- <header>
         <a href="https://windytron.com"><img src="logo.png" alt="Logo"></a>
        <p><a href="https://www.tindie.com/products/20426/" class="link">WindyTron Jumbo in Stock!</a></p>
    </header> -->
    <header>
        <div class="header-container">
            <img src="logo.png" alt="Logo" class="logo">
            <div class="header-text">
                <p><a href="https://www.tindie.com/products/20426/" class="link">WindyTron Jumbo in Stock!</a></p>
            </div>
        </div>
    </header>
    <div class="container">
        <div id="stamp"></div>

        <div class="section-title">Maui North Side</div>
        <table>
            <tr>
                <td width="50%"><b>Kanaha</b></td>
                <td><span id="kanaha" style="font-size: 18px; font-weight: bold;"></span></td>
            </tr>
            <tr>
                <td><b>Skill Village</b></td>
                <td>
                    <div id="skillvillage" style="font-weight: bold;"></div>
                </td>
            </tr>
            <tr>
                <td><a href="https://www.ndbc.noaa.gov/station_page.php?station=51205" class="link"><b>Pauwela
                            Buoy</b></a>(Ground Swell, Wind Swell)</td>
                <td><span id="north_swell"></span><br><span id="north_wind_swell"></span></td>
            </tr>
            <tr>
                <td><a href="https://www.ndbc.noaa.gov/station_page.php?station=51001" class="link"><b>51001
                            Buoy</b></a> (12 Hr Forecast)</td>
                <td><span id="north_swell_forecast"></span></td>
            </tr>
            <tr>
                <td><b>Surfline Forecast</b></td>
                <td>
                    <a href="https://www.surfline.com/surf-report/kanaha/5842041f4e65fad6a7708de5" class="link"><span
                            id="kanaha_waves"></span></a><br>
                    <a href="https://www.surfline.com/surf-report/ho-okipa/5842041f4e65fad6a7708de8" class="link"><span
                            id="hookipa_waves"></span></a>
                </td>
            </tr
            </tr>
        </table>
        <img id="wctide_img" class="float-right" width="100%" src="https://wildc.net/wind/kahului_tide_graph.gif"
            alt="Tide Graph">

        <div id="maui_surf"></div>

        <div class="section-title">Maui South Side</div>
        <table>
            <tr>
                <td><b>Kihei</b></td>
                <td><span id="kihei"></span></td>
            </tr>
            <tr>
                <td><a href="https://www.ndbc.noaa.gov/station_page.php?station=51213" class="link"><b>Lanai Buoy</b></a></td>
                <td><span id="south_swell"></span></td>
            </tr>
            <tr>
                <td><b>Surfline Forecast:</b></td>
                <td>
                    <div id="olowalu"></div>
                    <div id="launiupoko"></div>
                    <div id="lahaina"></div>
                </td>
            </tr>
        </table>
    </div>

    <div class="footer">
        <p>Velocity Units in MPH</p>
        <p>Comments or anomalies: <object class="svg-email-protection" data="qq.svg" type="image/svg+xml"></object></p>
        <p><a href="index_noad.html" class="link">Old Page</a></p>
    </div>
</body>
</html>
