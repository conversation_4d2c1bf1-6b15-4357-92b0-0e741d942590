Revision history for Perl extension Net::MQTT::Simple.

Incompatible changes are marked with "!!". Incompatibility with and changes in
"undocumented features" are not always specifically mentioned here.

1.26  2021-05-17 16:22
        - Fix: Packet parsing timing (Contributed by <PERSON><PERSON><PERSON><PERSON> via email)

1.25  2021-02-04 19:30
        - Fix: Remove callbacks on unsubscribe. (Contributed by <PERSON><PERSON><PERSON><PERSON><PERSON>
          via GitHub)
        - Fix: Separate client ID per instance. (Contributed by hollie via
          GitHub)

1.24  2019-07-22 16:47
        - Fix: Unsubscribe didn't work with newer mosquitto.
        - Fix: The procedural interface broke in 1.23 (use strict related).
        - Change: retroactively updated changelog; the apparent bug in
          Mosquitto was actually a bug fix, triggering a bug in
          Net::MQTT::Simple that was caused by misinterpretation of the MQTT
          3.1.1 specifications.

1.23  2019-04-29 18:18
        - New: env variables MQTT_SIMPLE_USERNAME and MQTT_SIMPLE_PASSWORD
        - New: undocumented global $Net::MQTT::Simple::PROTOCOL_LEVEL.
          (Contributed by <PERSON><PERSON><PERSON><PERSON><PERSON> via GitHub)
        - Fix: will no longer send invalid SUBSCRIBE message with a packet
          identifier of 0. (See change note in 1.24)
    !!  - Now depends on a sane Perl installation with strict and warnings
    !!  - Default protocol version is now 3.1.1
    !!  - Client identifier is now random instead of PID based

1.22  2018-12-01 21:01
        - New: username/password authentication (login method)
        - Changelog now uses ISO dates
        - New: last_will and disconnect (Contributed by nicramage via Github)
        - Bug fix: connection was not restored until after the reconnect
          interval.

1.21  2015-09-12 01:32
        - New: options --count, --message-only, --one in command line utility
        - Bug fix: enable autoflush in utility, for better pipe compatibility

1.20  2015-02-05 13:22
        - Bug fix: /# also matched any topic that matches + (tests were wrong)
          (Reported by ral in #mqtt)
        - Bug fix: Subscription topics weren't UTF8-encoded
        - Improvement: tests for topics "" and "/"
        - Code cleanups
        - New: Support for unsubscribing

1.16  2014-05-30 23:45
        - Bug fix: Clear buffer on reconnect to avoid garbage.
        - New: Reconnect on ping timeout.

1.15  2014-05-27 22:35
        - Bug fix: Client certificates now work.
        - Improvement: Pattern tests now includes those from mosquitto.
          (Suggested by karlp in #mqtt)

1.14  2014-05-27 13:09
    !!  - Bug fix: Unlike topics, message payloads are binary data, not text
          strings. This module no longer encodes or decodes them as UTF-8.
          A warning will be emitted if you try to publish data that is not
          binary compatbile. This change has no impact on 7 bit ASCII data.

1.13  2014-05-27 02:39
        - New: SSL support
        - New: --except option for mqtt-simple command line utility
        - New: --raw option for mqtt-simple command line utility
        - New: --no-color option for mqtt-simple command line utility
        - Improvement: mqtt-simple enables color by default, for terminals
        - Improvement: mqtt-simple filters ASCII control codes
        - Improvement: filter_as_regex is exposed and documented

1.12  2014-05-26 16:24
        - Bug fix: Update MANIFEST to include some new files
          (Reported by foobar in #revspace)
        - Bug fix: This file (Changes) mentioned the wrong distribution name
          (Reported by foobar in #revspace)
        - Improvement: Only try one reconnect per 5 seconds

1.11  2014-05-26 03:37
        - Bug fix: Calculations for next ping were bad, resulting in ping flood
        - Bug fix: Avoid some Perl warnings under perl -w
        - Improvement: Emit warnings on stderr if connection failed
        - Improvement: Prevent possible infinite recursion

1.10  2014-05-26 02:11
        - New: Support for MQTT subscriptions, with per-subscription handlers
        - New: Command line utility "mqtt-simple"
        - Improvement: Force reconnection after failed write to socket

1.01  2014-04-23 15:38
        - Bug fix: $\ is now ignored (e.g. under perl -l)

1.00  2014-04-22 23:59
        - Initial release
