# Simple example of sending and receiving values from Adafruit IO with the REST
# API client.
# Author: <PERSON>, <PERSON>

# Import Adafruit IO REST client.
from Adafruit_IO import Client, Feed, Data, RequestError
import datetime
import os
import sys

# Check if the filename is provided as a command-line argument
if len(sys.argv) < 2:
    print("Usage: python script.py <filename>")
    sys.exit(1)

# Get the filename from the command-line arguments
filename = sys.argv[1]

# Extract the basename (without extension) from the filename
basename = os.path.splitext(os.path.basename(filename))[0]

# Open the file and read the first line
with open(filename, "r") as f:
    d = f.readline().strip().split(',')

# f = open("kanaha.csv", "r")
# d = f.readline().split(',')

# Set to your Adafruit IO key.
# Remember, your key is a secret,
# so make sure not to publish it when you publish this code!
ADAFRUIT_IO_KEY = '0f0502cadd574ea390fe273d19971a12'

# Set to your Adafruit IO username.
# (go to https://accounts.adafruit.com to find your username)
ADAFRUIT_IO_USERNAME = 'tavdog'

# Create an instance of the REST client.
aio = Client(ADAFRUIT_IO_USERNAME, ADAFRUIT_IO_KEY)

#
# Adding data
#
print(d)
aio.send_data(f"wind-data.{basename}-csv",','.join(map(str, d)))
if basename == "kanaha":
    aio.send_data(f'wind-data.{basename}-average', d[0])
    aio.send_data(f'wind-data.{basename}-gust', d[1])
    aio.send_data(f'wind-data.{basename}-lull', d[2])
    aio.send_data(f'wind-data.{basename}-dir-card', d[3])
    aio.send_data(f'wind-data.{basename}-dir-deg', d[4])

