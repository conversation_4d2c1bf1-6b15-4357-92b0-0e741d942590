import json
import os
import time as tm

# generate dual display output for canaries spots

with open("el_cotillo.json", 'r') as f:
    el_cotillo = json.load(f)
with open("majanicho.json") as f:
    majanicho = json.load(f)

# dualwind

dualwind = dict()
dualwind['type'] = "dualwind"
dualwind['stamp'] = el_cotillo['stamp']
dualwind['label'] = el_cotillo['label']
dualwind['label2'] = majanicho['label']
dualwind['wind'] = f"{el_cotillo['dir_card']} {el_cotillo['dir_deg']} {el_cotillo['avg']}g{el_cotillo['gust']}"
dualwind['wind2'] = f"{majanicho['dir_card']} {majanicho['dir_deg']} {majanicho['avg']}g{majanicho['gust']}"
dualwind['aux1'] = f"{el_cotillo['aux1']}"
dualwind['aux1_2'] = f"{majanicho['aux1']}"

print(dualwind)

with open('elcotillo_majanicho.json', 'w') as f:
    json.dump(dualwind, f, separators=(',', ':'))

