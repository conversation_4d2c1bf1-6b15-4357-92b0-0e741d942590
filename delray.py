
import re
import os
import requests
import json

def deg2card(degree):
    # Round degree to nearest multiple of 11.25
    degree = round(degree / 11.25) * 11.25
    # Make an array for all the possible cardinal directions
    directions = ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE", 
                  "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]
    # Get the index of the direction that is closest to the degree
    index = int((degree + 11.25/2) // 11.25)
    # Return the cardinal direction
    return directions[index % 16]

headers = {
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'Accept-Language': 'en-US,en;q=0.9',
    'Connection': 'keep-alive',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'DNT': '1',
    'Origin': 'https://palmbeach.weatherstem.com',
    'Referer': 'https://palmbeach.weatherstem.com/fswndelraynorth',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
    'X-Requested-With': 'XMLHttpRequest',
    'sec-ch-ua': '"Chromium";v="110", "Not A(Brand";v="24", "Google Chrome";v="110"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
}

data = '{"id":1847,"lightning_interval":1,"lightning_distance":100}'

response = requests.post('https://palmbeach.weatherstem.com/station/get_current_weather', headers=headers, data=data)

data = json.loads(response.text)

#print(str(data))
gust = data["record"]["readings"][2]["value"]
avg = data["record"]["readings"][3]["value"]
# print(avg)
# print(gust)


dir_deg = data["record"]["readings"][14]["value"]
dir_card = deg2card(dir_deg)

# print(dir_card)
# print(dir_deg)


wind_string = dir_card + ' ' + str(dir_deg) + ' ' + str(avg)+'g'+str(gust)
print(wind_string)
previous = ""
with open('delray.txt',mode='r') as f:
    previous = f.read()
if (previous != wind_string):

    with open("delray.txt", "w") as text_file:
        text_file.write(wind_string)

    os.system(". ~/.bashrc ; cd ~/wildc.net/wind ; echo "+wind_string+" | ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass '$$titz*' -p delray/wind -r")



