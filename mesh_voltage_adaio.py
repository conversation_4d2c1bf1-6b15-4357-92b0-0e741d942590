# Persistent mqtt client, watch for voltage packets and publish them to io

# Import Adafruit IO REST client.
from Adafruit_IO import Client, Feed, Data, RequestError
import datetime
import paho.mqtt.client as mqtt
import os
import sys
import json


def publish_voltage(name,voltage):
# Create an instance of the REST client.
    aio = Client('tavdog', '0f0502cadd574ea390fe273d19971a12')
    feed = aio.feeds(name)
    print(feed.key + " : " + str(voltage))
    aio.send_data(feed.key, voltage)


mqttClient = mqtt.Client("mesh_client_voltage")
mqttClient.username_pw_set("windytron", 'jun1windyblow')
mqttClient.connect('gt.wildc.net', 1884)
mqttClient.loop_start()
# mqttClient.subscribe('mesh/2/json/MauiMesh/!da9e94c0')
mqttClient.subscribe('mesh/2/json/MauiMesh/!387e0248')


node_db = { 1839130823 : "gh",
            2330585902: 'ayla', 
            }

def on_message(client, userdata, message):
    print("\n")
    print("message received " ,str(message.payload.decode("utf-8")))
    print("\n")
    data = json.loads(str(message.payload.decode("utf-8")))
    if data['type'] == 'telemetry':
        #print("got telemetry")
        if 'from' in data and data['from'] in node_db.keys():
            # do the doings
            # round the voltage to 2 decimal places
            #print("publishing voltage")
            publish_voltage(
                node_db[data['from']],
                round(data['payload']['voltage'], 2)
            )


mqttClient.on_message = on_message

def on_log(client, userdata, level, buf):
    print("log: ",buf)

#mqttClient.on_log=on_log

while(True):
    pass






