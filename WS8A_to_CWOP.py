import os
import requests
from datetime import datetime, timezone
import time

print("---------------------------------")
print(f"     PST: {datetime.now().isoformat()}")
print(f"     UTC: {datetime.now(timezone.utc).isoformat()}")

# File path
file_path = 'WS8A.csv'

file_mtime = os.path.getmtime(file_path)
current_time = time.time()  # Get the current time in seconds since the epoch
# Check if the file has been updated within the last 5 minutes (300 seconds)
if current_time - file_mtime >= 300:
    print("File is stale (5 minutes), quitting early.")
    exit()

# Get the last modified time of the file in epoch format
last_modified_epoch = os.path.getmtime(file_path)

last_modified_iso = datetime.utcfromtimestamp(last_modified_epoch).isoformat() + "Z"
print(f"File UTC: {last_modified_iso}")
# Read the CSV file and split the data by commas
with open(file_path, 'r') as file:
    data = file.readline().strip().split(',')

# Extract values from the CSV data
avg_wind = data[0]  # Average wind speed (e.g., 13.1 mph)
gust_wind = data[1]  # Wind gust speed (e.g., 16.3 mph)
lull_wind = data[2]  # Lull wind speed (e.g., 10.7 mph) (not used here)
wind_cardinal = data[3]  # Wind cardinal direction (e.g., ENE)
wind_dir_deg = data[4]  # Wind direction degree (e.g., 69)
tempc = float(data[5])
tempf = (tempc * 9/5) + 32

# Format the URL with the provided ID and other parameters
url = (
    f"https://send.cwop.rest/?id=GW5395&lat=20.938017&long=-156.347942"
    f"&time={last_modified_iso}&tempf={tempf}&windspeedmph={avg_wind}"
    f"&windgustmph={gust_wind}&winddir={wind_dir_deg}"
)



# Send the GET request
response = requests.get(url)

# Print the response status
print(f"GET request sent to: {url}")
print(f"Response status code: {response.status_code}")
print(f"Response body : {response.text}")
