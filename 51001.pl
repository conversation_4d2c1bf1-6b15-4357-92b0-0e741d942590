#!/usr/bin/perl
use strict;
#my $station_id = "51205"; # pauwela

#my $station_id = "51201"; # waimea

#my $rawrss = `wget --cache=off -o /dev/null -O - http://www.ndbc.noaa.gov/data/latest_obs/$station_id.rss`;
#my $url =  "http://www.ndbc.noaa.gov/data/latest_obs/".$station_id.".rss";
my $url = "http://www.ndbc.noaa.gov/station_page.php?station=51001";
my $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
#my $rawrss = `cat pauwela.html`;
#my $rawrss = `cat 51205.rss`;
#print $rawrss;
chomp $rawrss;
#foreach my $line (split ('<strong',$rawrss)) {
#next unless ($line =~ /\.mp3/);
#$rawrss =~ /href="(\/news\S+)">/;
#$rawrss =~ /Wave\sHeight\s\(WVHT\):\<\/td\>\s+\<td\D*(\d+\.\d+\sft)\<\/td.*/;
$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Height is $height\n";

#$rawrss =~ /\<td\>Dominant\sWave\sPeriod\s\(DPD\):\<\/td\>\s+\<td\>\s+(\d+\s+sec)\<\/td\>/;

$rawrss =~ /\<td\>Swell\sPeriod\s\(SwP\):\<\/td\>\<td\>\s+(\d*\.\d\s+sec)/;


my $period = $1;
$period =~ s/\s//;
$period =~ s/sec/s/;

print "Period is $period\n";

#$rawrss =~ /MWD.+([ENSW]+\s+\(\d+)\D+176.*/;
#<td> NW ( 323 deg true )</td>

#$rawrss =~ /\<td\>Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\s+\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
$rawrss =~ /\<td\>Swell\sDirection\s\(SwD\):\<\/td\>\<td\>\s+([ENSW][ENSW]?+)/;
#$rawrss =~ /\<td\>Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\s+\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
my $direction = $1;
#print $direction;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;
print "Direction is $direction \n";
unless ($height eq '' || $period eq '' || $direction eq '') {
    my $LOG;
    open $LOG , ">51001.txt" ;	
    print $LOG $height.",". $period . "," . $direction . "\n";
    close $LOG;
}

