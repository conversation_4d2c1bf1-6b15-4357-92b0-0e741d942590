<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script>
$.ajaxSetup ({
    // Disable caching of AJAX responses
    cache: false
});

$(document).ready(function(){

  var currentDate = new Date().toISOString();

  // Construct the URL with the currentDate variable
  var wctide = `https://wildc.net/wind/kahului_tide_graph.gif?today=${currentDate}`;
  document.getElementById('wctide_img').src = wctide;
  const url = "https://ww.windalert.com/cgi-bin/tideGraph.gif";
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth() + 1;
  const day = today.getDate();
  tomorrow = new Date();
  tomorrow.setDate(today.getDate() + 1);
  const tyear = tomorrow.getFullYear();
  const tmonth = tomorrow.getMonth() + 1;
  const tday = tomorrow.getDate();
  const tideSUrl = url + `&beginTime=${year}-${month}-${day}%2000:00:00&endTime=${tyear}-${tmonth}-${tday}%2023:59:59&width=830&height=290&tideSiteName=Lahaina, Maui Island, Hawaii&cb=1685592015765`;
  const tideNUrl = url + `&beginTime=${year}-${month}-${day}%2000:00:00&endTime=${tyear}-${tmonth}-${tday}%2023:59:59&width=830&height=290&tideSiteName=Kahului,+Kahului+Harbor,+Hawaii`;
  console.log(tideNUrl);
  const imgN = document.getElementById("tideGraph_N");
  const imgS = document.getElementById("tideGraph_S");
  // imgN.src = tideNUrl;
  // imgS.src = tideSUrl;

    setInterval(timingLoad, 30000);    
    function timingLoad() {
        $("#kanaha").load("kanaha_lag", function(responseTxt, statusTxt, xhr){window.document.title = $("#kanaha").text();});
        $("#kihei").load("kihei", function(responseTxt, statusTxt, xhr){});
        $("#stamp").load("wind.stamp", function(responseTxt, statusTxt, xhr){});
        $("#kanaha_waves").load("sl_kanaha.txt", function(responseTxt, statusTxt, xhr){});
        $("#hookipa_waves").load("sl_hookipa.txt", function(responseTxt, statusTxt, xhr){});
        $("#lahaina").load("sl_lahaina.txt", function(responseTxt, statusTxt, xhr){});
        $("#olowalu").load("sl_olowalu.txt", function(responseTxt, statusTxt, xhr){});
        $("#launiupoko").load("sl_launiupoko.txt", function(responseTxt, statusTxt, xhr){});
        $("#ntide").load("kahului_tides_full.txt", function(responseTxt, statusTxt, xhr){});
        $("#north_swell").load("pauwela.txt", function(responseTxt, statusTxt, xhr){});
        $("#north_swell_forecast").load("51001.txt", function(responseTxt, statusTxt, xhr){});
        $("#south_swell").load("lanai_buoy.txt", function(responseTxt, statusTxt, xhr){});
        $("#maui_surf").load("maui_surf.html", function(responseTxt, statusTxt, xhr){});
//        window.document.title = $("#kanaha").text();      
  };
  timingLoad();
//  window.addEventListener('load', (event) => {
//     timingLoad(); 
//});
});
</script>
<style>
    
.svg-email-protection {
  width: 180px;
  height: 24px;
  vertical-align: middle;
}
    
</style>
</head>
<body>
<img src="logo.png"><br><a href="https://www.tindie.com/products/20426/">WindyTron Jumbo in Stock!</a><br>
<img id="wctide_img" style="float: right;" width=500 src="https://wildc.net/wind/kahului_tide_graph.gif" />
<div id="stamp"></div>
<b>Kanaha</b><div id="kanaha" style="font-size: 24px; font-weight: bold;"></div>
<a href="https://www.ndbc.noaa.gov/station_page.php?station=51205"><b>Pauwela Buoy</b></a>
<div id="north_swell"></div>
<a href="https://www.ndbc.noaa.gov/station_page.php?station=51001"><b>51001 Buoy</b></a> (12 Hr Forecast)
<div id="north_swell_forecast"></div>
<b>Surfline Forecast</b>
<a href="https://www.surfline.com/surf-report/kanaha/5842041f4e65fad6a7708de5"><div id="kanaha_waves"></div></a>
<a href="https://www.surfline.com/surf-report/ho-okipa/5842041f4e65fad6a7708de8"><div id="hookipa_waves"></div></a>
<div style="float: right;" id="maui_surf"></div>
<br>North Tides<div id="ntide"></div><br>
<!-- <img style="float: right;" width=400 id="tideGraph_N" /> -->

<b>Kihei</b> <div id="kihei"></div>
<!-- <b>Lanai Buoy</b> -->
<a href="https://www.ndbc.noaa.gov/station_page.php?station=51213"><b>Lanai Buoy</b></a>
<div id="south_swell"></div>
<b>Surfline Forecast:</b>
<div id="olowalu"></div>
<div id="launiupoko"></div>
<div id="lahaina"></div>

Velocity Units in MPH<br>

<p>Comments or anomalies: <object class="svg-email-protection" data="qq.svg" type="image/svg+xml"></object></p>


</body>
</html>

