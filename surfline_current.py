#!/usr/bin/python3
import requests
import json
import datetime
import pytz
import sys
import os
import time

try:
    spot_name = sys.argv[1]
except:
    print("Pass in all or a spot name")
    exit

spotdict = {
    "hookipa" : "5842041f4e65fad6a7708de8",
    "kanaha": "5842041f4e65fad6a7708de5",
    "waiehu": "6328ed0ed05d38031be2733c",
    "olowalu" : "5842041f4e65fad6a7708de0",
    "launiupoko" : "640f37c26d5ecc82715b1e36",
    "lahaina" : "5842041f4e65fad6a7708b37",
    "ukumehame" : "64bbf6d76914b76a8170b24d"
  
}
spotdata = dict()
if spot_name not in spotdict and spot_name  != "all":
    print("Bad spot name")
    exit()
# Get the HST timezone object.
hst_timezone = pytz.timezone("US/Hawaii")
# Get the current hour in PST.
hst_month = datetime.datetime.now(hst_timezone).month 
hst_hour = datetime.datetime.now(hst_timezone).hour
hst_day = datetime.datetime.now(hst_timezone).day
print("hour in hawaii is : " + str(hst_hour))

for key in spotdict.keys():
    #print("checking " + key)
    if key == spot_name or spot_name == "all":
#        print("doing " + key)

        # check for current file that is less then 24 hours old
        filename = "surfline_{}.json".format(key)
        print("checking file : " + filename)
        cache_good = False
        if os.path.isfile(filename):
            mtime = os.path.getmtime(filename)
            # Convert the modification time to a `datetime` object.
            datetime_object = datetime.datetime.fromtimestamp(mtime,hst_timezone)
            with open(filename) as f:
                try:
                    # Load the JSON data into a Python object.
                    data = json.load(f)
                    c_day = datetime_object.day
                    c_hour = datetime_object.hour
                    #print("fileday is : " + str(c_day))
                    if hst_day == c_day and hst_hour - c_hour < 5: # (5 hour updates)
                        cache_good = True
                except:
                    # cache_good is still False so if we catch an error we'll just fetch the data and overwrite
                    pass
                    
        if cache_good:
            print("Using Cache")

        else:
            print("Stale Cache, sleep 10")
            time.sleep(10)
            spot_id = spotdict[key]
            url_base = "https://services.surfline.com/kbyg/spots/forecasts/wave?spotId={}&intervalHours=1&days=1"
            url = url_base.format(spot_id)
            print("url : " + url)
            response = requests.get(url)
            json_string = response.text
            data = json.loads(json_string)
            with open(filename, "w") as f:
                json.dump(data, f)


        fcasts = data['data']['wave'] # array

        # get the current hour and use that as our index
        #print(fcasts[hst_hour]['surf'])
        with open("sl_" + key + ".txt", "w") as f:
            string = "{} : {} - {} ft".format(key,fcasts[hst_hour]['surf']['min'],fcasts[hst_hour]['surf']['max'])
            print(string)
            spotdata[key] = "{}-{} ft".format(fcasts[hst_hour]['surf']['min'],fcasts[hst_hour]['surf']['max'])
            f.write(string)

# write out surf spot json files.
spotdata["stamp"] = datetime.datetime.now(hst_timezone).strftime("%Y-%m-%dT%H:%M:%S") #str(hst_month) + '/' + str(hst_day)
with open("sl_" + spot_name + ".json", "w") as f:
    f.write(json.dumps(spotdata))

        
        


