import requests
import json

# Define the API endpoint and parameters
url = "https://waterservices.usgs.gov/nwis/iv/"
params = {
    "format": "json",
    "sites": "14105700",
    "parameterCd": "00060,00065",
    "siteStatus": "all"
}

# Make the API request
response = requests.get(url, params=params)
data = response.json()

# Initialize the result dictionary
result = {
    "site_name": "",
    "discharge_cfs": None,
    "gage_height_ft": None,
    "timestamp": ""
}

# Extract the relevant data
try:
    time_series = data["value"]["timeSeries"]
    for series in time_series:
        variable_desc = series["variable"]["variableDescription"]
        site_name = series["sourceInfo"]["siteName"]
        values = series["values"][0]["value"]
        if values:
            latest_value = values[-1]
            value = latest_value["value"]
            timestamp = latest_value["dateTime"]
            result["site_name"] = site_name
            result["timestamp"] = timestamp
            if "Discharge" in variable_desc:
                result["discharge_cfs"] = float(value)
            elif "Gage height" in variable_desc:
                result["gage_height_ft"] = float(value)

    # Output the result to a JSON file
    with open("dalles.json", "w") as f:
        json.dump(result, f, indent=4)

    print("Saved data to the_dalles_instantaneous_data.json")
    print(json.dumps(result, indent=4))

except Exception as e:
    print("Error parsing data:", e)