import time
import paho.mqtt.client as mqtt
import os
import sys

mqttClient = mqtt.Client("windytron_mqtt")
mqttClient.username_pw_set("windytron_publish", '$$titz*')
mqttClient.connect('gt.wildc.net', 1884)
mqttClient.loop_start()

#print "doing mqtt\n";
#system(". ~/.bashrc ; cd ~/wildc.net/wind ; ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass '\$\$titz*' -p maui/wind -r < wind.txt ;  ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass '\$\$titz*' -p maui/wind_swells -r < wind_swells.txt; ");
files = (   "wind.txt",
            "wind_swells.txt",
            "kanaha_pauwela_51001.txt",
            "kanaha_pauwela_da.txt",
            "wind_kts.txt",
            "kanaha_pauwela_tides.txt",
            "kihei_lanai_tides.txt",
            "smart.json",
            "wind_kts_pauwela_tides.txt",
            "kanaha_nswell_ntides.json",
            "kihei_sswell_stides.json",
            "north_tides.json",
            "south_tides.json",
            "molokini.json",
            "kailua.json",
            "makapuu.json",
        )

# check for a passed in filename and only do that one file
if len(sys.argv) > 1:
    filename = sys.argv[1]
    files = (filename,)


for f in files:

    topic = "maui/" + f.split('.')[0]
    exists = os.path.isfile(f)
    if exists:
        in_file = open(f, "r")
        msg = in_file.read().rstrip()
        info = mqttClient.publish(
            topic=topic,
            payload=msg.encode('utf-8'),
            qos=0,
            retain=True,
        )
        info.wait_for_publish()
        if info.is_published():
            print("Published topic: " + topic + " : " + msg)
    else:
        print("file {} does not exist".format(f))
        
mqttClient.loop_stop()    #Stop loop 
mqttClient.disconnect()