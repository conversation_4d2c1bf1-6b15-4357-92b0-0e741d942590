#!/usr/bin/perl
use strict;
#my $station_id = "51205"; # pauwela

#my $station_id = "51201"; # waimea
#my $station_id = "51210"; # kaneohe bay

#my $rawrss = `wget --cache=off -o /dev/null -O - http://www.ndbc.noaa.gov/data/latest_obs/$station_id.rss`;
#my $url =  "http://www.ndbc.noaa.gov/data/latest_obs/".$station_id.".rss";
my $url = "https://www.ndbc.noaa.gov/station_page.php?station=51210";
my $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
chomp $rawrss;
#print $rawrss;

$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Height is $height\n";
# unless ($height) {
#     print "No Data : Fetching Waimea instead.";
#     $url = "https://www.ndbc.noaa.gov/station_page.php?station=51210";
#     $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
#     chomp $rawrss;
#     $rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
#     $height = $1;
#     $height =~ s/\s//;
#     $height =~ s/ft/f/;
#     print "Height is $height\n";
# }

#  Dominant Wave Period (DPD):</td><td>   8 sec</td>
$rawrss =~ /\s+Dominant\sWave\sPeriod\s\(DPD\):\<\/td\>\<td\>\s+(\d+\s+sec)\<\/td\>/;
#.+(\d+\.\d?\s+sec)\<\/td/;
my $period = $1;
$period =~ s/\s//;
$period =~ s/sec/s/;


print "Period is $period\n";

#$rawrss =~ /MWD.+([ENSW]+\s+\(\d+)\D+176.*/;
#<td> NW ( 323 deg true )</td>
# Mean Wave Direction (MWD):</td><td>ENE ( 62 deg true )</td>
$rawrss =~ /\s+Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;

my $direction = $1;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;
print "Data is ".$height.",". $period . "," . $direction . "\n";
unless ($height eq '') { # || $period eq '' || $direction eq '') {
	print "writing kaneohe.txt";
    my $LOG;
    open $LOG , ">kaneohe.txt" ;	
    print $LOG $height.",". $period . "," . $direction . "\n";
    close $LOG;
}

