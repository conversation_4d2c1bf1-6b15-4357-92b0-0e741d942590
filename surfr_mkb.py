#!/usr/bin/python3

import json
import requests
import os
import time
import sys
import argparse

#setup argument parser
parser = argparse.ArgumentParser()
parser.add_argument("-d", "--debug", help="debug mode", action='store_true')
parser.add_argument("-t", "--tidbyt", help="output to tidbyt with key", action='store_true')
args = parser.parse_args() 
debug = args.debug
tidbyt = True #args.tidbyt always do tidbyt, thanks.

# set the request url to the competition servlet
url = 'https://kiter-271715.appspot.com/CompetitionServlet?spotid=10613&ajax=0&video=0'

sample_string = '{"name":"TAVIS","country":"null","height":12.1,"airtime":5.8,"distance":44,"kmu":56,"landinggforce":4.9,"base64":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/"}'
last_data_string = ''
data = None
# each second, get the url and parse the json response
count = 0
status = "cleared"

print("Started")
while True:

    if count > 9 and status == "displayed":
        # execute tidbyt delete command
        command = '~/wildc.net/wind/pixlet/tidbyt_surfr_delete.sh'
        if debug: print(command)
        os.system(command)
        status = "cleared"

    if debug:
        data = json.loads(sample_string)
    else:
        if debug: print("Getting data")
        response = requests.get(url)
        # parse the json response
        if debug: print('Response: ' + response.text)
        data = json.loads(response.text)

    # if the data is not empty, and base64 key is present, delete the base64 key
    if data:
        if 'base64' in data:
            del data['base64']
        # if data is different from the last data, print the data
        if json.dumps(data) != last_data_string:
            # print the data
            print('.')
            print(data)
            last_data_string = json.dumps(data)
            
            if tidbyt:
                # ./pixlet render surfr.star data='{"name":"TAVIS","country":"null","height":8.7,"airtime":5.8,"distance":44,"kmu":41,"landinggforce":4.9}'
                command = 'cd ~/wildc.net/wind/pixlet ; ./pixlet render surfr.star data=\'' + json.dumps(data) + '\' && ./tidbyt_surfr.sh'
                if debug: print(command)
                os.system(command)
                count = 0
                status = "displayed"

            # build the system command
            command = '. ~/.bashrc ; cd ~/wildc.net/wind ; ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass \$\$titz\* -p maui/surfr_mkb -r -m {}'.format(json.dumps(data))
            # print the commmand
            if debug: print(command)
            # execute system command
            os.system(command)
            print('+', end='')
        else:
            print('-',end='')

    # if debug is true, quit the program
    if debug:
        break

    # wait one second
    time.sleep(1)
    print('.', end='')
    sys.stdout.flush()
    count = count + 1

    
