import yaml
import json
import pprint
import ik_multi as ik
from datetime import datetime, timedelta, timezone

current_time = datetime.now(timezone.utc)
data = yaml.safe_load("wt.yaml")
with open("wt.yaml", "r") as f:
    data = yaml.safe_load(f)
# print(yaml.dump(data, default_flow_style=False))
pprint.pprint(data['inputs'])

print(f"got input keys: {data['inputs'].keys()}")

# for each input populate the last stamp from the json file
for input_name, input_dict in data["inputs"].items():
    print(f"{input_name}.json : ", end='')
    try:
        with open(f"{input_name}.json", "r") as f:
            j = json.load(f)
            if "utc_stamp" in j:
                input_dict["utc_stamp"] = j["utc_stamp"]
                print(f"{input_dict['utc_stamp']}")
            else:
                print("none")
    except Exception as e:
        print(f"no file {e}")

    # Set the needs_update flags
    input_dict["needs_update"] = False
    # Get the interval in minutes (default to 5 if not specified)
    interval_min = input_dict.get("interval_min", 5)
    
    # Check if we need to update this input based on timestamp
    if "utc_stamp" in input_dict:
        try:
            # Parse the timestamp assuming UTC
            stamp = input_dict["utc_stamp"]
            last_update = datetime.fromisoformat(stamp).replace(tzinfo=timezone.utc)
            
            # Calculate time difference in minutes
            time_diff = (current_time - last_update).total_seconds() / 60
            # print(f"{input_name} time dif : {time_diff}")
            # Only include if older than interval_min
            input_dict["needs_update"] = time_diff >= interval_min
        except (ValueError, TypeError) as e:
            # If timestamp parsing fails, assume we need to update
            print(f"Error parsing timestamp '{input_dict.get('stamp', 'None')}': {e}")
            input_dict["needs_update"] = True


# pprint.pprint(data["inputs"])

# get all the inputs with the provider of weatherflow that need updating
# (timestamp older than inter_min minutes or no timestamp)

inputs_weatherflow = {}
# Use UTC explicitly for current time

for input_name, input_dict in data["inputs"].items():
    if input_dict["provider"] == "weatherflow" and input_dict["needs_update"]:
        inputs_weatherflow[input_name] = input_dict

# Write out json files for each spot
results = ik.main(spots_dict=inputs_weatherflow,debug=True)
for name, data in results.items():
    with open(f"{name}.json", "w") as f:
        json.dump(data, f, indent=2)


for key in results:
    print(f"doing {key}")
    if key in data['inputs']:
        data['inputs'][key]["data"] = results[key]
    else:
        print(f"Warning: Result key '{key}' not found in inputs")

pprint.pprint(data)
