#!/usr/bin/env python3
import ik_multi
import json

# Test the new format
new_format_spots = {
    'kailua': {
        'data_types': ['wind', 'temperature'],
        'id': 429,
        'interval_min': 5,
        'needs_update': True,
        'provider': 'weatherflow',
        'type': 'anemo',
        'utc_stamp': '2025-06-28T00:34:36'
    }
}

print("Testing new format with debug data...")
result = ik_multi.main(spots_dict=new_format_spots, debug=True)

print("\nResults:")
for spot_name, data in result.items():
    print(f"\n{data['label']}:")
    print(f"  Wind: {data['dir_card']} {data['avg']}g{data['gust']} mph")
    print(f"  Direction: {data['dir_deg']}°")
    print(f"  Timestamp: {data['stamp']}")
    print(f"  Wind string: {data['wind_string']}")

# Test that old format still works
print("\n" + "="*50)
print("Testing old format for compatibility...")

old_format_spots = {
    "429": {"name": "kailua"}
}

result_old = ik_multi.main(spots_dict=old_format_spots, debug=True)

print("\nOld format results:")
for spot_name, data in result_old.items():
    print(f"\n{data['label']}:")
    print(f"  Wind: {data['dir_card']} {data['avg']}g{data['gust']} mph")
    print(f"  Direction: {data['dir_deg']}°")
    print(f"  Timestamp: {data['stamp']}")
    print(f"  Wind string: {data['wind_string']}")
