# get the wind via API call without storing the entire session object with dill

import requests
import re
import os
import sys
import json

spot_name = sys.argv[1]

spotdict = {
  "kanaha": "166192",
  "kahului": "4349",
  "kihei": "181145",
  "la_ventana": "183476"
}
try:
    spot_id = spotdict[spot_name]
except:
    #print("bad spot name")
    if spot_name.isdigit():
        #print ("trying as id")
        spot_id = spot_name
    else:
        exit(1)
        
api_url = 'http://api.weatherflow.com/wxengine/rest/spot/getSpotDetailSetByList?units_wind=mph&spot_list='+spot_id+'&wf_token='

# returns wftoken
def wf_login():
    #print( "doing full login" )
    post_data = {
        'isun' : '<EMAIL>',
        'ispw' : 'jungle',
        'iwok.x' : 'Sign In',
        'app' : '' ,
        'rd' : '' 
    }
    post_headers = {
        'User-Agent': 'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://secure.ikitesurf.com/?app=wx&rd=spot/166192',
        'Content-Type':  'application/x-www-form-urlencoded'
    }
    url1 = 'https://secure.ikitesurf.com/'
 
    #print("posting"+url1)
    resp2 = requests.head(url1, data=post_data)
    headers = str(resp2.headers)
    #print(headers)
    token = re.search("wfToken=(.*);expires", headers ).group(1)
    
    with open("wftoken", "w") as wf_token_file:
        wf_token_file.write(token)
    
    return token

def fetch_data(token):
    response = requests.get(api_url+token)
    result = response.text
#    print (response.headers)
#    print (result)
    j = json.loads(result)
    data = j["spots"][0]["stations"][0]["data_values"][0][2:7]
    if re.search("Invalid weatherflow token|paid membership",result) or data[0] is None:
        # token has gone stale, refresh
        token = wf_login()
        response = requests.get(api_url+token)
        result = response.text
        
    j = json.loads(result)
#    print (j)
    try:
        data = j["spots"][0]["stations"][0]["data_values"][0][2:7]
    except:
        print("bad json data structure")
        exit(1)
        
    # write out json data to file for retrieval by gto2 devices
    #with open(spot_name+'.json', "w") as json_file:
    #    json_file.write(result)      
    
    print(result)
    return data

    

# restore session object from filesystem
exists = os.path.isfile("wftoken")
if exists:
    in_file = open("wftoken", "r")
    wftoken = in_file.read().rstrip()
else:
    wftoken = wf_login()
    

data = fetch_data(wftoken)
exit(0)
        
# assign the wind data
#print(data)
#[2.1, 0.9, 3.6, 270,"W"]
# wind_string = data_array[1].strip('"')
wind_avg = int(data[0]+0.5)
wind_gust = int(data[2]+0.5)
wind_dir = data[4].strip('"')
wind_dir_degrees = data[3]
# #ENE 56 16g20
wind_string = str(wind_dir) + " " + str(wind_dir_degrees) + " " + str(wind_avg) + "g" + str(wind_gust)
print (wind_string)
# 
# # need to output this : ENE 56 16g20 , 2.0f,11s,NE47 - 2018-06-20T19:20:02
with open(spot_name, "w") as text_file:
    text_file.write(wind_string)
