import time
import paho.mqtt.client as mqtt
import os
import sys
import json
from datetime import datetime, timedelta


if len(sys.argv) != 2:
    print("Usage: python wt_mqtt.py <filelist.json>")
    exit(1)
else:
    filename = sys.argv[1]

print("opening {}".format(filename))
try:
    with open(filename, 'r') as json_file:
        files = json.load(json_file)
except Exception as e:
    print(e)
    exit(1)

mqttClient = mqtt.Client("windytron_mqtt")
mqttClient.username_pw_set("windytron_publish", '$$titz*')
mqttClient.connect('gt.wildc.net', 1884)
mqttClient.loop_start()


def is_not_stale(in_file):
    # Get the mtime of the file
    mtime = os.stat(in_file).st_mtime

    # Convert the mtime to a datetime object
    mtime_dt = datetime.fromtimestamp(mtime)

    # Check if the file is more than 10 minutes old
    if (datetime.now() - mtime_dt).total_seconds() > 600:
        return False
    else:
        return True

for t in files.keys():
    for f in files[t]:
        if '/' in t:
            topic = "{}".format(t)
        else:
            topic = "{}/{}".format(t, f.split('.')[0])
        exists = os.path.isfile(f)
        
        if exists and is_not_stale(f):
            in_file = open(f, "r")
            msg = in_file.read().rstrip()
            info = mqttClient.publish(
                topic=topic,
                payload=msg.encode('utf-8'),
                qos=0,
                retain=True,
            )
            info.wait_for_publish()
            if info.is_published():
                print("Published topic: " + topic + " : " + msg)
                # published_dict[topic] = msg
        else:
            print("file {} does not exist or is stale".format(f))
        
mqttClient.loop_stop()    #Stop loop 
mqttClient.disconnect()
