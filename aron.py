
import re
import os
import requests
from bs4 import BeautifulSoup

vgm_url = 'https://riverlevels.uk/arun-pulborough-pulborough-swan-bridge'
headers = {"User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36"}


# with open('aron-sample.html',mode='r') as f:
#     html_text = f.read()
html_text = requests.get(vgm_url,headers=headers).text
#print(html_text)

soup = BeautifulSoup(html_text, 'html.parser')

#tr_tags = soup.find_all(string="Delray Beach")[0].parent.parent.parent.find_all("table")[0].find_all('td')
h4_tags = soup.find_all(string="Current River Level: ")[0].parent.parent.parent.parent.find_all('h4')

#h4_tag = soup.find_all(string="Current River Level: ")[0].parent
#print(h4_tags[0])
level = re.search("(\d\.\d+m,\s\w+)", html_text)
# 1.358m, falling  need to change to 1.6m
levelm = float(level[0][:5])
#print(levelm)
levelm = round(levelm,1)
#print(levelm)
#levelm = levelm[0:3]
leveld = level[0][8:]

output = str(levelm) + "m, "+leveld
print(output)
#print(level[0])
#dir_card = re.search("(\w+)", dir_tag.string)[0]
#dir_deg = re.search("(\d+)", dir_tag.string)[0]
#print(dir_card)
#print(dir_deg)

#avg = re.search("(\d+)\)", vel_tag.string)[1]
#gust = re.search("-?\s+(\d+)\s+\(", vel_tag.string)[1]    # 	6 - 9 (avg: 8)
#print(avg)
#print(gust)

#print ("Content-type: text/html\n\n") 
# wind_string = dir_card + ' ' + dir_deg + ' ' + avg+'g'+gust
# #print(wind_string);
# previous = ""
# with open('delray.txt',mode='r') as f:
#     previous = f.read()
# if (previous != wind_string):
# 
#     with open("delray.txt", "w") as text_file:
#         text_file.write(wind_string)
# 
os.system(". ~/.bashrc ; cd ~/wildc.net/wind ; echo "+output+" | ./Net-MQTT-Simple/bin/mqtt-simple --insecure -h gt.wildc.net:1884 -u windytron_publish --pass '$$titz*' -p uk/riveraron -r")



