import time
import paho.mqtt.client as mqtt
import os
import sys

mqttClient = mqtt.Client("windytron_mqtt")
mqttClient.username_pw_set("windytron_publish", '$$titz*')
mqttClient.connect('gt.wildc.net', 1884)
mqttClient.loop_start()

files = ("ventana_2.txt",)

# check for a passed in filename and only do that one file
if len(sys.argv) > 1:
    filename = sys.argv[1]
    files = (filename,)

for f in files:
    topic = "mexico/la_ventana"
    exists = os.path.isfile(f)
    if exists:
        in_file = open(f, "r")
        msg = in_file.read().rstrip()
        info = mqttClient.publish(
            topic=topic,
            payload=msg.encode('utf-8'),
            qos=0,
            retain=True,
        )
        info.wait_for_publish()
        if info.is_published():
            print("Published topic: " + topic + " : " + msg)
    else:
        print("file {} does not exist".format(f))
        
mqttClient.loop_stop()    #Stop loop 
mqttClient.disconnect()