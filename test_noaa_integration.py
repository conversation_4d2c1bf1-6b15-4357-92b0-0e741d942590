#!/usr/bin/env python3
"""
Test the NOAA NDBC integration with the generalized provider system
"""

import yaml
import json
import pprint
import importlib
from datetime import datetime, timezone

# Load YAML configuration
with open("wt.yaml", "r") as f:
    data = yaml.safe_load(f)

print("Testing NOAA NDBC provider integration...")

# Force mokapu_point to need updating
data['inputs']['mokapu_point']['needs_update'] = True

# Test just the NOAA NDBC provider
provider = "noaa_ndbc"
provider_config = data['providers'][provider]

print(f"Provider config: {provider_config}")

# Collect inputs for this provider that need updating
provider_inputs = {}
for input_name, input_dict in data["inputs"].items():
    if input_dict["provider"] == provider and input_dict.get("needs_update", False):
        provider_inputs[input_name] = input_dict

print(f"Provider inputs: {provider_inputs}")

if provider_inputs:
    # Process using module-based provider
    try:
        module_name = provider_config['module']
        caller_name = provider_config['caller']
        
        print(f"Importing module: {module_name}")
        module = importlib.import_module(module_name)
        caller_func = getattr(module, caller_name)
        
        # Prepare arguments
        kwargs = {}
        if 'args' in provider_config:
            for arg in provider_config['args']:
                if arg == 'spots_dict':
                    kwargs['spots_dict'] = provider_inputs
                elif arg == 'debug':
                    kwargs['debug'] = False
        
        print(f"Calling {caller_name} with kwargs: {list(kwargs.keys())}")
        
        # Call the function
        results = caller_func(**kwargs)
        
        print(f"Results: {results}")
        
        # Write JSON files
        for name, spot_data in results.items():
            with open(f"{name}.json", "w") as f:
                json.dump(spot_data, f, indent=2)
            print(f"Wrote {name}.json")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
else:
    print("No inputs need updating for NOAA NDBC provider")
