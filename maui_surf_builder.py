#!/usr/bin/python3
import requests
import json
import datetime
import pytz
import sys
import os
import time
import paho.mqtt.client as mqtt

# combining north and south side spots into their own lists and json output

with open("sl_all.json") as f:
    spot_dict = json.load(f)

stamp = spot_dict["stamp"]

# north spots are:
north_spots = { "label": "North Surf",
                "type": "rows",
                "num_rows" : 3, 
                "stamp" : stamp,                
                "row1" : "Hookipa: " + spot_dict["hookipa"],
                "row2" : "Kanaha:  " + spot_dict["kanaha"],
                "row3" : "Waiehu:  " + spot_dict["waiehu"],

}

south_spots = { "label": "South Surf",
                "type": "rows",
                "num_rows" : 3,
                "stamp" : stamp,                
                "row1" : "Olowalu: " + spot_dict["olowalu"],
                "row2" : "Launiupoko:" + spot_dict["launiupoko"],
                "row3" : "Lahaina: " + spot_dict["lahaina"],
}

mqttClient = mqtt.Client("windytron_mqtt")
mqttClient.username_pw_set("windytron_publish", '$$titz*')
mqttClient.connect('gt.wildc.net', 1884)
mqttClient.loop_start()

msg = json.dumps(north_spots)
info = mqttClient.publish(
    topic="maui/north_surf",
    payload=msg.encode('utf-8'),
    qos=0,
    retain=True,
)
info.wait_for_publish()
if info.is_published():
    print("Published topic: maui/north_surf : " + msg)

msg = json.dumps(south_spots)
info = mqttClient.publish(
    topic="maui/south_surf",
    payload=msg.encode('utf-8'),
    qos=0,
    retain=True,
)
info.wait_for_publish()
if info.is_published():
    print("Published topic: maui/south_surf : " + msg)
        
mqttClient.loop_stop()    #Stop loop 
mqttClient.disconnect()

#{"stamp":"2023-05-05T17:50:12","label":"SouthSurf","type":"3rows","row1":"Olowalu:5-6ft","row2":"Launiupoko:6-7ft","row3":"Lahaina:7-9ft"}