from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi

uri = "mongodb+srv://windytron:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
# Create a new client and connect to the server
client = MongoClient(uri, server_api=ServerApi('1'))
db = client["windytron_stations"]
readings = db["kaa_readings"]

# Sort documents by timestamp in descending order and retrieve the latest document
latest_document = readings.find_one(sort=[('stamp', -1)])

if latest_document:
    # Process the latest document
    print(f"Latest document: {latest_document}")
else:
     print("No documents found in the collection.")

                    
