#!/usr/bin/python3

import json
import requests
import os
import time
import sys
import argparse

#function to convert degrees to cardinal direction
def deg_to_card(deg):
    if deg >= 348.75 or deg < 11.25:
        return "N"
    elif deg >= 11.25 and deg < 33.75:
        return "NNE"
    elif deg >= 33.75 and deg < 56.25:
        return "NE"
    elif deg >= 56.25 and deg < 78.75:
        return "ENE"
    elif deg >= 78.75 and deg < 101.25:
        return "E"
    elif deg >= 101.25 and deg < 123.75:
        return "ESE"
    elif deg >= 123.75 and deg < 146.25:
        return "SE"
    elif deg >= 146.25 and deg < 168.75:
        return "SSE"
    elif deg >= 168.75 and deg < 191.25:
        return "S"
    elif deg >= 191.25 and deg < 213.75:
        return "SSW"
    elif deg >= 213.75 and deg < 236.25:
        return "SW"
    elif deg >= 236.25 and deg < 258.75:
        return "WSW"
    elif deg >= 258.75 and deg < 281.25:
        return "W"
    elif deg >= 281.25 and deg < 303.75:
        return "WNW"
    elif deg >= 303.75 and deg < 326.25:
        return "NW"
    elif deg >= 326.25 and deg < 348.75:
        return "NNW"

#function round a float to an int
def mph_to_rounded_kts(x):
    return int(round(x*0.868,0))

debug = False
url = 'https://api.weather.com/v2/pws/observations/current?apiKey=e1f10a1e78da46f5b10a1e78da96f525&stationId=ITARIF23&format=json&units=e'
sample_string = '{"observations":[{"stationID":"ITARIF23","obsTimeUtc":"2022-05-03T08:56:44Z","obsTimeLocal":"2022-05-03 10:56:44","neighborhood":"Tarifa","softwareType":"GW1000_V1.6.8","country":"ES","solarRadiation":527.5,"lon":-5.614,"realtimeFrequency":null,"epoch":1651568204,"lat":36.021,"uv":5.0,"winddir":262,"humidity":77,"qcStatus":-1,"imperial":{"temp":62,"heatIndex":62,"dewpt":55,"windChill":62,"windSpeed":4,"windGust":4,"pressure":29.78,"precipRate":0.00,"precipTotal":0.00,"elev":5}}]}'
data = None
wind_data = {}
if debug:
    data = json.loads(sample_string)
    print("using: "+sample_string)
else:
    if debug: print("Getting data")
    response = requests.get(url)
    # parse the json response
        #print("using: " + response.text)
    if debug: print('Response: ' + response.text)
    data = json.loads(response.text)

if data:
    if 'observations' in data:
        if len(data['observations']) > 0:
            #print(data['observations'][0])
            #print("OK")
            data = data['observations'][0]
            wind_data['dir_deg'] = data["winddir"]
            wind_data['dir_card'] = deg_to_card(wind_data["dir_deg"])
            #wind_data['dir_card'] = "N"
            wind_data['wind_speed'] = mph_to_rounded_kts(data["imperial"]["windSpeed"])  # mph to knots
            wind_data['wind_gust'] =  mph_to_rounded_kts(data["imperial"]["windGust"])   # mph to knots

            print(wind_data)
        else:
            print("No data")
    else:
        print("No observations")


wind_string = str(wind_data['dir_card']) + " " + str(wind_data['dir_deg']) + " " + str(wind_data['wind_speed']) + "g" + str(wind_data['wind_gust'])

print(wind_string)

# write wind_string to file tarifa.txt
with open('tarifa.txt', 'w') as f:
    f.write(wind_string)


