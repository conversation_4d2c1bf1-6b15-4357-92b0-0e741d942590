#!/usr/bin/perl
use DateTime;
my $dt = DateTime->now(
      time_zone  => '-1000',
  );
  
  my $lanai = `cat lanai_buoy.txt`;
my $pauwela = `cat pauwela.txt`;
$pauwela =~ s/\s*$//g;
$lanai =~ s/\s*$//g;

my $json = '{
    "display_items_arr":[
        "  Pauwela:' . $pauwela . '  ",
        "  Lanai:'.$lanai . '  "
    ],
    "stamp": '. $dt . '"
}';
my $LOG;
open $LOG , ">pauwela+lanai.json" ;	
print $LOG $json . "\n";
close $LOG;
