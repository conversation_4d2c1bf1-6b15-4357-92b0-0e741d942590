# fetch tides for ka<PERSON><PERSON> and leave them in a text file format : ?????

import requests
import re
import os
import sys
import json
from datetime import datetime


print_debug = True
debug = False

NOAA_API_URL_HILO = "https://api.tidesandcurrents.noaa.gov/api/prod/datagetter?date=today&station=%s&product=predictions&datum=MLLW&time_zone=lst_ldt&units=english&format=json&interval=hilo"

def dp(arg):
    if print_debug:
        print(arg)

def get_tides_hilo(station_id):
    tides = {}
    url = NOAA_API_URL_HILO % (station_id)
    dp(url)
    if not debug:
        dp("HILO Url : " + url)
        resp = requests.get(url)
        if resp.status_code != 200:
            dp(str(resp))
        else:
            dp(str(resp))
            dp(resp.text)
            tides = json.loads(resp.text)
    else:  # in debug mode return None so main program will just use hilo data for graphing
        tides_json = """{"predictions": [{"t": "2022-03-04 00:19", "v": "1.630", "type": "H"}, {"t": "2022-03-04 11:05", "v": "-5.532", "type": "L"}, {"t": "2022-03-04 17:10", "v": "12.85", "type": "H"}, {"t": "2022-03-04 22:52", "v": "-1.058", "type": "L"}]}"""
        tides = json.loads(tides_json)
    return tides

#tides = get_tides_hilo("1615680") # kahului
tides = get_tides_hilo("TPT2797") # maalaea
#tides = get_tides_hilo("TPT2799") # lahaina

t_array = []
formatted_array = []
# parse date strings and pick the daylight two only
for item in tides["predictions"]:
    datetime_obj = datetime.strptime(item["t"], '%Y-%m-%d %H:%M')
    
    # format all the data to rounded float and am/pm
    formatted_item = {}
    formatted_item["type"] = item["type"]
    formatted_item["v"] = round(float(item["v"]),1)  # need to replace negative sign with something else. will use ~
    if (float(item["v"])) < 0:  # use the value before it's rounded to check for if there is a negative
        # delete the negative sign and change the tide type to 'N' (negative)
        formatted_item["v"] = str(formatted_item["v"]).replace('-','')
        formatted_item["type"] = 'N'
    formatted_item["t"] = datetime_obj.strftime('%H%M')
    
    formatted_array.append(formatted_item)

    if datetime_obj.hour < 6:
        continue # skip to the next if the first tide is earlier than 6 am
    # add to t_array
    if len(t_array) < 2:
        t_array.append(formatted_item)
print(str(formatted_array))
print(str(t_array))
if len(t_array) < 2 and len(formatted_array) > 1:
    print("doing slice")
    t_array = formatted_array[0:2]

if len(t_array) > 1: # if we have two tides then print them, if not then just print the first one
    output = "%s%s%s~" % (t_array[0]["t"],t_array[0]["type"],t_array[0]["v"]) + "%s%s%s" % (t_array[1]["t"],t_array[1]["type"],t_array[1]["v"])
else:
    output = "%s%s%s" % (t_array[0]["t"],t_array[0]["type"],t_array[0]["v"])

dp(output)

with open('kihei_tides.txt', 'w') as f:
    f.write(output)
full_dict = dict()
full_dict["label"] = "South Tides"
full_dict["type"] = "tides"
full_dict["num_tides"] = len(formatted_array)
full_dict["stamp"] = tides["predictions"][0]["t"].split(' ')[0] # split on space, use only the date portion
output = ""
i = 0
for tide in formatted_array:
    full_dict["tide"+str(i)] = "%s %s %s ft" % (tide["t"],tides["predictions"][i]["type"],round(float(tides["predictions"][i]["v"]),1))
    i += 1
    output += "%s %s %s ft ~ " % (tide["t"],tide["type"],tide["v"])

output = ""
for tide in formatted_array:
    output += "%s %s %s ft ~ " % (tide["t"],tide["type"],tide["v"])
print("full tide: " + output)
with open('kihei_tides_full.txt', 'w') as f:
    f.write(output)

with open('south_tides.json', 'w') as f:
    f.write(json.dumps(full_dict))
