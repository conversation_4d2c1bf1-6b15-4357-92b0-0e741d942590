use warnings;
use strict;
use Mail::POP3Client;
use IO::Socket::SSL;
use CGI qw(:standard);
use DateTime;
my $dt = DateTime->now(
      time_zone  => '-1000',
  );
my $LOG ;
my $test = 0;

my $wind = `cat kihei`;
chomp $wind;

# get the buoy wave data
my $waves = `cat lanai_buoy.txt`;
chomp $waves;

my $on_the_half = 0;
# update the windlog on the half hour only
if (($dt->minute() == 0 || $dt->minute() == 30) || $test) {
	$on_the_half = 1;
	
}

my $quarter = int($dt->minute() / 15);

# and update the avg_history file with 5 minute average
if (($dt->hour >= 10 && $dt->hour <= 18) || $test) {  # 8 hours 10-6
    	my $avg_str = `cat avg_history_south.txt`;
    	chomp $avg_str;
    	print $avg_str . "\n";
    	my @avg_array = split //, $avg_str;
    	print "hour is " . $dt->hour();
    	my $step = int(($dt->hour()-10) * 4 + (int($dt->minute)/15));
    	$wind =~ m/\s(\d\d)?g/;
        print " step is $step , new_val is $1 \n";
    	
    	my $new_val = $1;
    	my $old_val = $avg_array[$step]*5;
    	print "old_val is $old_val \n";
    	# calculate the average
    	my $five_step = int(($dt->minute() - ($quarter * 15)) / 5);
    	print "five_step is $five_step \n";
    	my $new_avg = int((($old_val * $five_step + $new_val) / ($five_step+1))/5);
    	
        $avg_array[$step] = $new_avg;
    	print "graph new_avg is " . $new_avg . "\n"; 
        
        #print "new array is @avg_array";
        my $avgoutput = join("",@avg_array);
        print $avgoutput;
        system("echo $avgoutput > avg_history_south.txt");
    
}

my $graph = `cat avg_history_south.txt`;
chomp $graph;
#my $wdir_deg = get_wind_degrees();
#$wind =~ s/\s/ $wdir_deg /;  # insert the degrees (replace first space with space degrees space)
print "wind is " . $wind . "\n";
open $LOG , ">wind_south.txt" ;	
print $LOG $wind." , ". $waves . " - ".$dt . "+" . $graph . "\n";
close $LOG;

if ($on_the_half) {
    system "mv windlog_south.txt windlog_south.tmp";
    system "cp wind_south.txt windlog_south.txt";
    system "cat windlog_south.tmp >> windlog_south.txt";
}



exit;
