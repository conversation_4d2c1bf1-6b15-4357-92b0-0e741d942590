#!/usr/bin/perl
use strict;
use CGI qw(param);
use LWP::Simple;

my $buoy_id = param("buoy_id");
$buoy_id = "51205" if $buoy_id eq '';
print("buoy+id: " . $buoy_id);
$buoy_id =~ s/[^ [:ascii:] ]//g;


my @fields = qw(STN LAT LON YYYY MM DD hh mm WDIR WSPD GST WVHT DPD APD MWD PRES PTDY ATMP WTMP DEWP VIS TIDE);
my $latest_obs = `cat latest_obs.txt`;
my @buoy_data;
foreach my $line (split('\n',$latest_obs)){
    #print($line);
    my @data = split(/\s+/,$line);
    if ($data[0] eq $buoy_id){ 
        @buoy_data = @data;
        print("found buoy data @data");
        last;
    }
}

my $json = '{';

my $count = 0;
foreach my $field (@buoy_data) {
    print("doing $field \n");
    $json .= '"'. $fields[$count] .'" : "' . $field . '"'.",\n" if ($field ne 'MM' && $field ne '');
    $count++;

}
$json .= '}';


print $json;

