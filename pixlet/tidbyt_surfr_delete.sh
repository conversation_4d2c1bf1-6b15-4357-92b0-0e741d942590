#!/bin/sh

# delete
curl --request DELETE --url https://m1pro.local:8000/v0/devices/9abe2858/installations/test --header "Authorization: a" --header 'Content-Type: application/json' 

# curl --request DELETE --url https://api.tidbyt.com/v0/devices/worryingly-benign-unlimited-crane-f05/installations/surfrlb --header "$(cat kitebit.key)" --header 'Content-Type: application/json' 

# curl --request DELETE --url https://api.tidbyt.com/v0/devices/worryingly-benign-unlimited-crane-f05/installations/surfr --header "$(cat kitebit.key)" --header 'Content-Type: application/json' 

# curl --request DELETE --url https://api.tidbyt.com/v0/devices/worryingly-benign-unlimited-crane-f05/installations/surfr --header "$(cat kitebit.key)" --header 'Content-Type: application/json' 
