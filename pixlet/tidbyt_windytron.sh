#!/bin/sh
curl -X POST --url https://api.tidbyt.com/v0/devices/northerly-prodigious-bright-coatimundi-778/push \
--header "Authorization: $(cat gen2.key)" \
--header 'Content-Type: application/json' --data '{"image": "'$(base64 -w 0 -i windytron.webp)'","installationID": "windytron"}'


#!/bin/sh
curl -X POST --url https://api.tidbyt.com/v0/devices/northerly-prodigious-bright-coatimundi-778/push \
--header "Authorization: $(cat gen2.key)" \
--header 'Content-Type: application/json' --data '{"image": "'$(base64 -w 0 -i windytron.webp)'","installationID": "windytron"}'
