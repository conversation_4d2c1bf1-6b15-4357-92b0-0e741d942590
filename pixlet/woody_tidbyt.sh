#!/bin/sh
# curl --request POST --url 'https://api.tidbyt.com/v0/devices/forthrightly-essential-steadfast-pronghorn-016/push' \
# --header "$(cat woody_tidbyt.key)" \
# --header 'Content-Type: application/json' \
# --data '{"image": "'$(base64 -w 0 -i windytron_kihei.webp)'","installationID": "windytronkihei"}'

# curl --request POST --url 'https://api.tidbyt.com/v0/devices/forthrightly-essential-steadfast-pronghorn-016/push' --header "$(cat woody_tidbyt.key)" --header 'Content-Type: application/json' --data '{"image": "'$(base64 -w 0 -i windytron.webp)'","installationID": "windytronkanaha"}'

#./pixlet push {} {} -b -t {} -i {}".format(device['api_id'], webp_path, device['api_key'], app['iname'])

./pixlet render windytron_kihei.star && ./pixlet render windytron.star && ./pixlet render windytron_hookipa.star

./pixlet push delinquently-logical-committed-phoenix-0da windytron_kihei.webp -t ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************** -i kiheitron
sleep 5
./pixlet push delinquently-logical-committed-phoenix-0da windytron.webp -t ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************** -i kanahatron
sleep 5
./pixlet push delinquently-logical-committed-phoenix-0da windytron_hookipa.webp -t ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************** -i hookipatron
