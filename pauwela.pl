#!/usr/bin/perl
use strict;
#my $station_id = "51205"; # pauwela

#my $station_id = "51201"; # waimea
#my $station_id = "51210"; # kaneohe bay

#my $rawrss = `wget --cache=off -o /dev/null -O - http://www.ndbc.noaa.gov/data/latest_obs/$station_id.rss`;
#my $url =  "http://www.ndbc.noaa.gov/data/latest_obs/".$station_id.".rss";
my $url = "https://www.ndbc.noaa.gov/station_page.php?station=51205";
my $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
chomp $rawrss;

$rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Height is $height\n";
unless ($height) {
    print "No Data : Fetching Waimea/Kanehoe instead.";
    $url = "https://www.ndbc.noaa.gov/station_page.php?station=51201"; # waimea
    # $url = "https://www.ndbc.noaa.gov/station_page.php?station=51210"; # kaneohe
    $rawrss = `wget --timeout=20 --cache=off -o /dev/null -O - $url`;
    chomp $rawrss;
    $rawrss =~ /Swell\sHeight\s\(SwH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
    $height = $1;
    $height =~ s/\s//;
    $height =~ s/ft/f/;
    print "Height is $height\n";
}


#$rawrss =~ /Dominant\sWave\sPeriod\s\(DPD\):\<\/td\>\<td\>\s+(\d+\s+sec)\<\/td\>/;
$rawrss =~ /Swell\sPeriod\s\(SwP\):\<\/td\>\<td\>\s+(\d+)\.?\d?\s+sec\s?\<\/td\>/;


#.+(\d+\.\d?\s+sec)\<\/td/;
my $period = $1;
$period .= "s";
$period =~ s/\s//;
$period =~ s/sec/s/;


print "Period is $period\n";

#$rawrss =~ /MWD.+([ENSW]+\s+\(\d+)\D+176.*/;
#<td> NW ( 323 deg true )</td>
#$rawrss =~ /Mean\sWave\sDirection\s+\(MWD\):\<\/td\>\<td[^ENSW]*([ENSW]+\s+\(\s+\d+).*+/;
$rawrss =~ /Swell\sDirection\s\(SwD\):\<\/td\>\<td[^ENSW]*([ENSW]+).*+/;


my $direction = $1;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;
print "Data is ".$height.",". $period . "," . $direction . "\n";
unless ($height eq '') { # || $period eq '' || $direction eq '') {
	print "writing pauwela.txt\n";
    my $LOG;
    open $LOG , ">pauwela.txt" ;	
    print $LOG $height.",". $period . "," . $direction . "\n";
    close $LOG;
}


# wind swell "Wind Wave Height (WWH):""
$rawrss =~ /Wind\sWave\sHeight\s\(WWH\):\<\/td\>\<td\D*(\d+\.\d+\sft)\<\/td.*/;
my $height = $1;
$height =~ s/\s//;
$height =~ s/ft/f/;
print "Wind Height is $height\n";

$rawrss =~ /Wind\sWave\sPeriod\s\(WWP\):\<\/td\>\<td\>\s+(\d+)\.?\d?\s+sec\s?\<\/td\>/;

#.+(\d+\.\d?\s+sec)\<\/td/;
my $period = $1;
$period .= "s";
$period =~ s/\s//;
$period =~ s/sec/s/;


print "Wind Period is $period\n";

$rawrss =~ /Wind\sWave\sDirection\s\(WWD\):\<\/td\>\<td[^ENSW]*([ENSW]+).*+/;

my $direction = $1;
$direction =~ s/\(//;
$direction =~ s/\s//;
$direction =~ s/\s//;

print "Wind Direction is $direction\n";
# Wave Steepness (STEEPNESS):
# $rawrss =~ /Wave\sSteepness\s\(STEEPNESS\):\<\/td\>\ (\w*) \<\/td\>/;
$rawrss =~ /Wave\sSteepness\s\(STEEPNESS\):.*?<td>\s*(\w+)<\/td>/;

my $steepness = $1;
# $steepness =~ s/\(//;
# $steepness =~ s/\s//;
# $steepness =~ s/\s//;

print "Wind Steepness is $steepness\n";

print "Wind Swell is ".$height.",". $period . "," . $direction . "," . $steepness ."\n";
unless ($height eq '') { # || $period eq '' || $direction eq '') {
	print "writing pauwela_wind_swell.txt\n";
    my $LOG;
    open $LOG , ">pauwela_wind_swell.txt" ;	
    print $LOG $height.",". $period . "," . $direction. "," . $steepness ."\n";
    close $LOG;

    # run io updater curl.
    my $csv = $height.",". $period . "," . $direction. "," . $steepness;
    my $curl_command = "curl -F 'value=$csv' -H 'X-AIO-Key: 0f0502cadd574ea390fe273d19971a12'   https://io.adafruit.com/api/v2/tavdog/feeds/buoy-data.pauwela-csv/data";
    print $curl_command;
    system($curl_command);
}





